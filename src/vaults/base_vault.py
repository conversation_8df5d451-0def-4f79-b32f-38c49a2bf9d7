"""
Base vault class for modular vault implementations.

This module provides the base class that all vault implementations should inherit from.
It defines the common interface and shared functionality for vault operations.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from decimal import Decimal
from log import get_logger

logger = get_logger(__name__)


class BaseVault(ABC):
    """
    Abstract base class for vault implementations.
    
    This class defines the interface that all vault implementations must follow.
    It provides common functionality and enforces the implementation of required methods.
    """
    
    def __init__(self, vault_config: Dict[str, Any]):
        """
        Initialize the base vault.
        
        Args:
            vault_config: Vault-specific configuration dictionary
        """
        self.vault_config = vault_config
        self.vault_name = vault_config.get("VAULT_NAME", "Unknown")
        self.vault_address = vault_config.get("VAULT_ADDRESS")
        self.chain_id = vault_config.get("PRIMARY_CHAIN_ID")
        
        if not self.vault_address:
            raise ValueError("VAULT_ADDRESS is required in vault configuration")
        if not self.chain_id:
            raise ValueError("PRIMARY_CHAIN_ID is required in vault configuration")
            
        logger.info(f"Initialized {self.vault_name} vault at {self.vault_address}")
    
    @abstractmethod
    def get_market_names(self) -> Dict[str, str]:
        """
        Get mapping of market IDs to human-readable names.
        
        Returns:
            Dictionary mapping market IDs to names
        """
        pass
    
    @abstractmethod
    def get_priority_list(self) -> List[str]:
        """
        Get priority list for market allocation.
        
        Returns:
            List of market IDs in priority order
        """
        pass
    
    @abstractmethod
    def get_target_utilization(self) -> Dict[str, Decimal]:
        """
        Get target utilization ratios for each market.
        
        Returns:
            Dictionary mapping market IDs to target utilization ratios
        """
        pass
    
    @abstractmethod
    def get_min_utilization_delta_bips(self) -> int:
        """
        Get minimum utilization delta in basis points.
        
        Returns:
            Minimum utilization delta in basis points
        """
        pass
    
    def get_vault_address(self) -> str:
        """Get the vault address."""
        return self.vault_address
    
    def get_chain_id(self) -> int:
        """Get the chain ID."""
        return self.chain_id
    
    def get_vault_name(self) -> str:
        """Get the vault name."""
        return self.vault_name
    
    def validate_configuration(self) -> bool:
        """
        Validate the vault configuration.
        
        Returns:
            True if configuration is valid, False otherwise
        """
        try:
            # Check required methods return valid data
            market_names = self.get_market_names()
            priority_list = self.get_priority_list()
            target_util = self.get_target_utilization()
            min_delta = self.get_min_utilization_delta_bips()
            
            # Validate market names
            if not isinstance(market_names, dict) or not market_names:
                logger.error("Market names must be a non-empty dictionary")
                return False
            
            # Validate priority list
            if not isinstance(priority_list, list) or not priority_list:
                logger.error("Priority list must be a non-empty list")
                return False
            
            # Validate target utilization
            if not isinstance(target_util, dict) or not target_util:
                logger.error("Target utilization must be a non-empty dictionary")
                return False
            
            # Validate min delta
            if not isinstance(min_delta, int) or min_delta < 0:
                logger.error("Min utilization delta must be a non-negative integer")
                return False
            
            # Check that all markets in priority list have target utilization
            missing_targets = set(priority_list) - set(target_util.keys())
            if missing_targets:
                logger.error(f"Missing target utilization for markets: {missing_targets}")
                return False
            
            logger.info(f"Configuration validation passed for {self.vault_name}")
            return True
            
        except Exception as e:
            logger.error(f"Configuration validation failed for {self.vault_name}: {e}")
            return False
    
    def get_lambda_config(self) -> Dict[str, Any]:
        """
        Get Lambda-specific configuration for this vault.
        
        Returns:
            Dictionary with Lambda configuration
        """
        return {
            "function_name": self.vault_config.get("LAMBDA_FUNCTION_NAME", f"morpho-reallocator-{self.vault_name.lower()}"),
            "schedule": self.vault_config.get("LAMBDA_SCHEDULE", "rate(1 hour)"),
            "timeout": self.vault_config.get("LAMBDA_TIMEOUT", 300),
            "memory": self.vault_config.get("LAMBDA_MEMORY", 1024),
        }
