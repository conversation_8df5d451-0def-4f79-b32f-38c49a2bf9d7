"""
Lambda handler for OpenEden vault reallocation.

This module provides the AWS Lambda handler function for the OpenEden vault,
integrating the vault-specific configuration with the main bot logic.
"""

import json
import traceback
from typing import Dict, Any
from log import get_logger
from src.vaults.openeden.vault import create_openeden_vault
from src.bot import MorphoReallocatorBot
from utils.load_cloud_env import load_cloud_env

logger = get_logger(__name__)


def lambda_handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    AWS Lambda handler for OpenEden vault reallocation.
    
    This function is the entry point for the Lambda function that handles
    reallocation for the OpenEden vault. It creates the vault instance,
    initializes the bot with vault-specific configuration, and executes
    the reallocation logic.
    
    Args:
        event: Lambda event data
        context: Lambda context object
        
    Returns:
        Dictionary with execution results
    """
    try:
        logger.info("Starting OpenEden vault reallocation")
        logger.info(f"Event: {json.dumps(event, default=str)}")
        
        # Load cloud environment variables
        load_cloud_env()
        
        # Create OpenEden vault instance
        vault = create_openeden_vault()
        logger.info(f"Created vault: {vault.get_vault_name()} at {vault.get_vault_address()}")
        
        # Initialize bot with vault configuration
        bot = MorphoReallocatorBot(
            vault_address=vault.get_vault_address(),
            chain_id=vault.get_chain_id(),
            market_names=vault.get_market_names(),
            priority_list=vault.get_priority_list(),
            target_util=vault.get_target_utilization(),
            min_utilization_delta_bips=vault.get_min_utilization_delta_bips(),
            fordefi_vault_id=vault.get_fordefi_vault_id(),
            vault_name="openeden"
        )
        
        # Execute reallocation
        result = bot.run()
        
        # Prepare response
        response = {
            "statusCode": 200,
            "body": {
                "vault_name": vault.get_vault_name(),
                "vault_address": vault.get_vault_address(),
                "chain_id": vault.get_chain_id(),
                "execution_result": result,
                "message": "OpenEden vault reallocation completed successfully"
            }
        }
        
        logger.info(f"OpenEden vault reallocation completed: {response}")
        return response
        
    except Exception as e:
        error_message = f"OpenEden vault reallocation failed: {str(e)}"
        logger.error(error_message)
        logger.error(f"Traceback: {traceback.format_exc()}")
        
        return {
            "statusCode": 500,
            "body": {
                "error": error_message,
                "traceback": traceback.format_exc()
            }
        }


def local_test():
    """
    Local test function for development and debugging.
    
    This function can be used to test the Lambda handler locally
    without deploying to AWS Lambda.
    """
    try:
        # Mock Lambda event and context
        mock_event = {
            "source": "aws.events",
            "detail-type": "Scheduled Event",
            "detail": {}
        }
        
        class MockContext:
            def __init__(self):
                self.function_name = "morpho-reallocator-openeden"
                self.function_version = "$LATEST"
                self.invoked_function_arn = "arn:aws:lambda:us-east-1:123456789012:function:morpho-reallocator-openeden"
                self.memory_limit_in_mb = "1024"
                self.remaining_time_in_millis = lambda: 300000
        
        mock_context = MockContext()
        
        # Execute handler
        result = lambda_handler(mock_event, mock_context)
        
        print("Local test completed successfully:")
        print(json.dumps(result, indent=2, default=str))
        
    except Exception as e:
        print(f"Local test failed: {e}")
        print(f"Traceback: {traceback.format_exc()}")


if __name__ == "__main__":
    # Run local test when script is executed directly
    local_test()
