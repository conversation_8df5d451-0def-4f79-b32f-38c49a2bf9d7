"""
Vault factory for creating and managing vault instances.

This module provides a factory pattern for creating vault instances
and managing the registry of available vaults.
"""

from typing import Dict, List, Type, Optional
from src.vaults.base_vault import BaseVault
from src.vaults.openeden.vault import OpenEdenVault
from src.vaults.usdc_reactor.vault import USDCReactorVault
from log import get_logger

logger = get_logger(__name__)


class VaultFactory:
    """
    Factory class for creating and managing vault instances.
    
    This class maintains a registry of available vault types and provides
    methods to create vault instances, list available vaults, and validate
    vault configurations.
    """
    
    # Registry of available vault types
    _vault_registry: Dict[str, Type[BaseVault]] = {
        "openeden": OpenEdenVault,
        "usdc_reactor": USDCReactorVault,
    }
    
    @classmethod
    def register_vault(cls, vault_name: str, vault_class: Type[BaseVault]) -> None:
        """
        Register a new vault type in the factory.
        
        Args:
            vault_name: Name identifier for the vault
            vault_class: Vault class that inherits from <PERSON><PERSON>ault
        """
        if not issubclass(vault_class, BaseVault):
            raise ValueError(f"Vault class {vault_class.__name__} must inherit from <PERSON><PERSON><PERSON>")
        
        cls._vault_registry[vault_name.lower()] = vault_class
        logger.info(f"Registered vault type: {vault_name}")
    
    @classmethod
    def create_vault(cls, vault_name: str) -> BaseVault:
        """
        Create a vault instance by name.
        
        Args:
            vault_name: Name of the vault to create
            
        Returns:
            Configured vault instance
            
        Raises:
            ValueError: If vault name is not registered or creation fails
        """
        vault_name_lower = vault_name.lower()
        
        if vault_name_lower not in cls._vault_registry:
            available_vaults = list(cls._vault_registry.keys())
            raise ValueError(f"Unknown vault type: {vault_name}. Available vaults: {available_vaults}")
        
        vault_class = cls._vault_registry[vault_name_lower]
        
        try:
            vault = vault_class()
            
            # Validate the vault configuration
            if not vault.validate_configuration():
                raise ValueError(f"Vault configuration validation failed for {vault_name}")
            
            logger.info(f"Successfully created vault: {vault_name}")
            return vault
            
        except Exception as e:
            logger.error(f"Failed to create vault {vault_name}: {e}")
            raise ValueError(f"Failed to create vault {vault_name}: {e}")
    
    @classmethod
    def list_available_vaults(cls) -> List[str]:
        """
        Get list of available vault types.
        
        Returns:
            List of registered vault names
        """
        return list(cls._vault_registry.keys())
    
    @classmethod
    def get_vault_info(cls, vault_name: str) -> Dict[str, str]:
        """
        Get information about a specific vault type.
        
        Args:
            vault_name: Name of the vault
            
        Returns:
            Dictionary with vault information
            
        Raises:
            ValueError: If vault name is not registered
        """
        vault_name_lower = vault_name.lower()
        
        if vault_name_lower not in cls._vault_registry:
            raise ValueError(f"Unknown vault type: {vault_name}")
        
        vault_class = cls._vault_registry[vault_name_lower]
        
        return {
            "name": vault_name,
            "class_name": vault_class.__name__,
            "module": vault_class.__module__,
            "doc": vault_class.__doc__ or "No documentation available"
        }
    
    @classmethod
    def validate_all_vaults(cls) -> Dict[str, bool]:
        """
        Validate configuration for all registered vaults.

        Returns:
            Dictionary mapping vault names to validation results
        """
        results = {}

        for vault_name in cls._vault_registry.keys():
            try:
                vault = cls.create_vault(vault_name)
                results[vault_name] = vault.validate_configuration()
            except Exception as e:
                logger.error(f"Failed to validate vault {vault_name}: {e}")
                results[vault_name] = False

        return results

    @classmethod
    def get_vault_name_by_address(cls, vault_address: str) -> Optional[str]:
        """
        Get vault name by vault address.

        Args:
            vault_address: The vault address to look up

        Returns:
            Vault name if found, None otherwise
        """
        vault_address_lower = vault_address.lower()

        for vault_name in cls._vault_registry.keys():
            try:
                vault = cls.create_vault(vault_name)
                if vault.get_vault_address().lower() == vault_address_lower:
                    return vault_name
            except Exception as e:
                logger.error(f"Failed to check vault {vault_name}: {e}")
                continue

        return None


def create_vault_by_name(vault_name: str) -> BaseVault:
    """
    Convenience function to create a vault by name.
    
    Args:
        vault_name: Name of the vault to create
        
    Returns:
        Configured vault instance
    """
    return VaultFactory.create_vault(vault_name)


def list_vaults() -> List[str]:
    """
    Convenience function to list available vaults.
    
    Returns:
        List of available vault names
    """
    return VaultFactory.list_available_vaults()


# Example usage and testing
if __name__ == "__main__":
    # List available vaults
    print("Available vaults:", list_vaults())
    
    # Create OpenEden vault
    try:
        openeden_vault = create_vault_by_name("openeden")
        print(f"Created vault: {openeden_vault.get_vault_name()}")
        print(f"Vault address: {openeden_vault.get_vault_address()}")
        print(f"Chain ID: {openeden_vault.get_chain_id()}")
    except Exception as e:
        print(f"Failed to create OpenEden vault: {e}")
    
    # Validate all vaults
    validation_results = VaultFactory.validate_all_vaults()
    print("Validation results:", validation_results)
