"""
USDC Reactor vault implementation.

This module provides the USDC Reactor-specific vault implementation that inherits
from the base vault class and implements all required methods.
"""

from typing import Dict, List
from decimal import Decimal
from src.vaults.base_vault import BaseVault
from config.vaults.usdc_reactor.config import (
    market_names,
    priority_list,
    target_util,
    get_min_utilization_delta_bips,
    VAULT_NAME,
    VAULT_ADDRESS,
    PRIMARY_CHAIN_ID,
    FORDEFI_VAULT_ID
)
from log import get_logger

logger = get_logger(__name__)


class USDCReactorVault(BaseVault):
    """
    USDC Reactor vault implementation.
    
    This class implements the BaseVault interface for the USDC Reactor vault,
    providing all the specific configuration and behavior for this vault.
    """
    
    def __init__(self):
        """Initialize the USDC Reactor vault with its specific configuration."""
        vault_config = {
            "VAULT_NAME": VAULT_NAME,
            "VAULT_ADDRESS": VAULT_ADDRESS,
            "PRIMARY_CHAIN_ID": PRIMARY_CHAIN_ID,
            "FORDEFI_VAULT_ID": FORDEFI_VAULT_ID,
            "LAMBDA_FUNCTION_NAME": "morpho-reallocator-usdc-reactor",
            "LAMBDA_SCHEDULE": "rate(1 hour)",
            "LAMBDA_TIMEOUT": 300,
            "LAMBDA_MEMORY": 1024,
        }
        super().__init__(vault_config)
    
    def get_market_names(self) -> Dict[str, str]:
        """
        Get mapping of market IDs to human-readable names for USDC Reactor vault.
        
        Returns:
            Dictionary mapping market IDs to names
        """
        return market_names.copy()
    
    def get_priority_list(self) -> List[str]:
        """
        Get priority list for market allocation for USDC Reactor vault.
        
        Returns:
            List of market IDs in priority order
        """
        return priority_list.copy()
    
    def get_target_utilization(self) -> Dict[str, Decimal]:
        """
        Get target utilization ratios for each market in USDC Reactor vault.
        
        Returns:
            Dictionary mapping market IDs to target utilization ratios
        """
        return target_util.copy()
    
    def get_min_utilization_delta_bips(self) -> int:
        """
        Get minimum utilization delta in basis points for USDC Reactor vault.
        
        Returns:
            Minimum utilization delta in basis points
        """
        return get_min_utilization_delta_bips(self.chain_id)
    
    def get_fordefi_vault_id(self) -> str:
        """
        Get the Fordefi vault ID for this vault.
        
        Returns:
            Fordefi vault ID
        """
        return FORDEFI_VAULT_ID


def create_usdc_reactor_vault() -> USDCReactorVault:
    """
    Factory function to create and validate an USDC Reactor vault instance.
    
    Returns:
        Configured and validated USDC Reactor vault instance
        
    Raises:
        ValueError: If vault configuration is invalid
    """
    vault = USDCReactorVault()
    
    if not vault.validate_configuration():
        raise ValueError("USDC Reactor vault configuration validation failed")
    
    logger.info(f"Successfully created USDC Reactor vault: {vault.get_vault_address()}")
    return vault
