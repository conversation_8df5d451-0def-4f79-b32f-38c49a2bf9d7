from log import get_logger

from src.interfaces.market_interface import IMarketData
from utils.web3_contract import Web3Contract
from utils.web3_connector import Web3Connector
from config.shared.global_config import get_rpc_url
from config.shared.abis.morpho_abi import MORPHO_ABI, MORPHO_ADDRESS

logger = get_logger(__name__)

# Cache expiration time in seconds (5 minutes)
CACHE_EXPIRATION_SECONDS = 300


class MarketData(IMarketData):
    """
    Market data implementation using Web3 contract calls.

    This class implements the IMarketData interface and provides market data
    collection from Morpho Blue contracts via direct Web3 calls.
    """

    def __init__(self, market_id: str, chain_id: int = 8453, vault_address: str = None):
        """
        Initialize MarketData instance.

        Args:
            market_id: The market ID to fetch data for
            chain_id: Chain ID to fetch data from (default: Base)
            vault_address: Vault address for vault-specific operations (optional)
        """
        self.market_id = market_id
        self.chain_id = chain_id
        self.vault_address = vault_address

        # Get RPC URL for the chain
        rpc_url = get_rpc_url(chain_id)
        if not rpc_url:
            raise ValueError(f"No RPC URL configured for chain {chain_id}")

        self.client = Web3Connector(provider_url=rpc_url).get_client()
        self.morpho_contract = Web3Contract(self.client, MORPHO_ADDRESS, MORPHO_ABI)

        # Only create vault contract if vault address is provided
        if vault_address:
            from config.shared.abis.vault_abi import VAULT_ABI
            self.vault_contract = Web3Contract(self.client, vault_address, VAULT_ABI)
        else:
            self.vault_contract = None

        self.params = self.fetch_market_params(market_id)
        self.state = self.fetch_market_state(market_id)
        self.data = self.fetch_market_data(market_id)

    def fetch_market_data(self, market_id: str) -> dict:
        """
        Fetch complete market data using Web3 contract calls.

        This method implements the IMarketData.fetch_market_data interface.
        Returns data in the exact format specified by the interface.

        Args:
            market_id: The market ID to fetch data for

        Returns:
            Complete market data in interface format:
            {
                "chain_id": "",
                "id": "",
                "params": {
                    "loan_token": "",
                    "collateral_token": "",
                    "irm": "",
                    "oracle": "",
                    "lltv": "",
                },
                "state": {
                    "total_supply_assets": "",
                    "total_supply_shares": "",
                    "total_borrow_assets": "",
                    "total_borrow_shares": "",
                    "last_update": "",
                    "fee": "",
                },
                "cap": "",
                "vault_assets": "",
            }
        """        
        cap = self.vault_contract.call("config", [market_id])[0]
        position = self.morpho_contract.call("position", [market_id, config.VAULT_ADDRESS])
        supply_shares = position[0]

        total_supply_assets = self.state["total_supply_assets"]
        total_supply_shares = self.state["total_supply_shares"]
        vault_assets = (supply_shares * total_supply_assets) // total_supply_shares

        stringified_market_id = str(market_id.hex())
        stringified_market_id = "0x" + stringified_market_id if not stringified_market_id.startswith("0x") else stringified_market_id

        return {
            "chain_id": self.chain_id,
            "id": stringified_market_id,
            "params": self.params,
            "state": self.state,
            "cap": cap,
            "vault_assets": vault_assets,
            "rate_at_target": 0
        }

    def fetch_market_params(self, market_id: str) -> dict:
        """
        Fetch market parameters using Web3 contract calls.

        This method implements the IMarketData.fetch_market_params interface.
        Returns data in the exact format specified by the interface.

        Args:
            market_id: The market ID to fetch parameters for

        Returns:
            Market parameters in interface format:
            {
                "loan_token": "",
                "collateral_token": "",
                "irm": "",
                "oracle": "",
                "lltv": "",
            }
        """
        market_params = self.morpho_contract.call("idToMarketParams", [market_id])

        return {
            "loan_token": market_params[0],
            "collateral_token": market_params[1],
            "oracle": market_params[2],
            "irm": market_params[3],
            "lltv": market_params[4],
        }

    def fetch_market_state(self, market_id: str) -> dict:
        """
        Fetch market state using Web3 contract calls.

        This method implements the IMarketData.fetch_market_state interface.
        Returns data in the exact format specified by the interface.

        Args:
            market_id: The market ID to fetch state for

        Returns:
            Market state in interface format:
            {
                "total_supply_assets": "",
                "total_supply_shares": "",
                "total_borrow_assets": "",
                "total_borrow_shares": "",
                "last_update": "",
                "fee": "",
            }
        """
        market_state = self.morpho_contract.call("market", [market_id])

        return {
            "total_supply_assets": market_state[0],
            "total_supply_shares": market_state[1],
            "total_borrow_assets": market_state[2],
            "total_borrow_shares": market_state[3],
            "last_update": market_state[4],
            "fee": market_state[5],
        }
