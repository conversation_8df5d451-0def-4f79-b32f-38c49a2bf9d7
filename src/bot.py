import os
import sys
import json
import traceback
from typing import Dict, List, Any, Optional
from decimal import Decimal

sys.path.append(os.getcwd())

from src.vault_data import VaultData
from config.shared.global_config import get_rpc_url, WALLET_ADDRESS
from config.shared.abis.vault_abi import VAULT_ABI
from utils.web3_connector import Web3Connector
from utils.web3_contract import Web3Contract
from strategies.priority_util.priority_util import PrioritizedUtilizationStrategy
from utils.fordefi_transaction_helpers import build_and_send_transaction
from utils.load_cloud_env import load_cloud_env

from log import get_logger

logger = get_logger(__name__)

STRATEGY = PrioritizedUtilizationStrategy


class MorphoReallocatorBot:
    """
    Modular Morpho Reallocator Bot that works with any vault configuration.

    This class provides a generic reallocation bot that can work with any vault
    by accepting vault-specific configuration parameters.
    """
    def __init__(self, vault_address: str, chain_id: int, market_names: Dict[str, str],
                 priority_list: List[str], target_util: Dict[str, Decimal],
                 min_utilization_delta_bips: int, fordefi_vault_id: Optional[str] = None,
                 vault_name: Optional[str] = None):
        """
        Initialize the bot with vault-specific configuration.

        Args:
            vault_address: Address of the vault to manage
            chain_id: Chain ID where the vault is deployed
            market_names: Mapping of market IDs to human-readable names
            priority_list: List of market IDs in priority order
            target_util: Target utilization ratios for each market
            min_utilization_delta_bips: Minimum utilization delta in basis points
            fordefi_vault_id: Fordefi vault ID for transaction signing (optional)
            vault_name: Name of the vault for strategy configuration (optional)
        """
        self.vault_address = vault_address
        self.chain_id = chain_id
        self.market_names = market_names
        self.priority_list = priority_list
        self.target_util = target_util
        self.min_utilization_delta_bips = min_utilization_delta_bips
        self.fordefi_vault_id = fordefi_vault_id
        self.vault_name = vault_name

        # Determine vault name if not provided
        if not self.vault_name:
            try:
                from src.vaults.vault_factory import VaultFactory
                self.vault_name = VaultFactory.get_vault_name_by_address(vault_address)
                if self.vault_name:
                    logger.info(f"Auto-detected vault name: {self.vault_name}")
                else:
                    logger.warning(f"Could not determine vault name for address {vault_address}, using default")
                    self.vault_name = "openeden"  # Default fallback
            except Exception as e:
                logger.warning(f"Failed to auto-detect vault name: {e}, using default")
                self.vault_name = "openeden"  # Default fallback

        # Initialize strategy with vault-specific configuration
        self.strategy = STRATEGY(vault_name=self.vault_name)

        logger.info(f"Initialized bot for vault {vault_address} on chain {chain_id} (vault: {self.vault_name})")

    def run(self):
        """Execute reallocation and return result status."""
        try:
            logger.info(f"Starting reallocation process for vault {self.vault_address}...")

            # Fetch vault data
            logger.info("Fetching vault data...")
            vault_data = VaultData(
                vault_address=self.vault_address, chain_id=self.chain_id
            ).fetch_vault_data()

            # Get Web3 client
            logger.info("Connecting to Web3...")
            rpc_url = get_rpc_url(self.chain_id)
            if not rpc_url:
                raise ValueError(f"No RPC URL configured for chain {self.chain_id}")

            client = Web3Connector(provider_url=rpc_url).get_client()

            # Find reallocation strategy
            logger.info("Calculating reallocation strategy...")
            new_allocations = self.strategy.find_reallocation(vault_data)

            # Check if reallocation is needed
            if not new_allocations:
                logger.info("No reallocation needed")
                return {
                    "status": "success",
                    "message": "No reallocation needed",
                    "transaction_hash": None
                }

            # Format parameters to fit the contract function invocation
            params = []
            for new_allocation in new_allocations:
                params.append(
                    tuple(
                        [
                            tuple(list(new_allocation[0].values())),
                            int(new_allocation[1]),
                        ]
                    )
                )

            logger.info(f"Preparing transaction with {len(params)} allocations...")
            contract = Web3Contract(client, self.vault_address, VAULT_ABI)

            # Get wallet address from global config
            from config.shared.global_config import WALLET_ADDRESS
            if not WALLET_ADDRESS:
                raise ValueError("WALLET_ADDRESS not configured")

            function_name = "reallocate"
            params = [params]
            contract_function = getattr(contract.contract.functions, function_name)

            # Execute transaction
            logger.info("Executing reallocation transaction...")

            # Get Fordefi vault ID
            fordefi_vault_id = self.fordefi_vault_id
            if not fordefi_vault_id:
                # Try to get from global config as fallback
                try:
                    from config.shared.global_config import FORDEFI_VAULT_ID as GLOBAL_FORDEFI_VAULT_ID
                    fordefi_vault_id = GLOBAL_FORDEFI_VAULT_ID
                except ImportError:
                    pass

            if not fordefi_vault_id:
                raise ValueError("FORDEFI_VAULT_ID not provided and not found in global config")

            tx_hash = build_and_send_transaction(
                self.chain_id,
                fordefi_vault_id,
                client,
                contract_function,
                params,
                WALLET_ADDRESS,
            )

            logger.info(f"Transaction hash: {tx_hash}")
            return {
                "status": "success",
                "message": "Reallocation completed successfully",
                "transaction_hash": tx_hash
            }

        except Exception as e:
            error_msg = f"Reallocation failed: {str(e)}"
            logger.error(error_msg)
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {
                "status": "error",
                "message": error_msg,
                "transaction_hash": None
            }


class ReallocationBot:
    """
    Legacy compatibility class for backward compatibility.

    This class provides backward compatibility with the old bot interface
    while using the new modular architecture under the hood.
    """

    def __init__(self):
        """Initialize the legacy bot with OpenEden vault configuration."""
        # Import OpenEden configuration for backward compatibility
        try:
            from config.vaults.openeden.config import (
                VAULT_ADDRESS, PRIMARY_CHAIN_ID, market_names, priority_list,
                target_util, get_min_utilization_delta_bips, FORDEFI_VAULT_ID
            )

            self.bot = MorphoReallocatorBot(
                vault_address=VAULT_ADDRESS,
                chain_id=PRIMARY_CHAIN_ID,
                market_names=market_names,
                priority_list=priority_list,
                target_util=target_util,
                min_utilization_delta_bips=get_min_utilization_delta_bips(PRIMARY_CHAIN_ID),
                fordefi_vault_id=FORDEFI_VAULT_ID,
                vault_name="openeden"
            )
        except ImportError:
            # Fallback to legacy config if new config not available
            from config import config
            self.bot = MorphoReallocatorBot(
                vault_address=config.VAULT_ADDRESS,
                chain_id=config.PRIMARY_CHAIN_ID,
                market_names=config.market_names,
                priority_list=config.priority_list,
                target_util=config.target_util,
                min_utilization_delta_bips=config.vaults_min_utilization_delta_bips.get(
                    config.PRIMARY_CHAIN_ID, config.DEFAULT_MIN_UTILIZATION_DELTA_BIPS
                ),
                fordefi_vault_id=getattr(config, 'FORDEFI_VAULT_ID', None),
                vault_name="openeden"  # Default to openeden for legacy compatibility
            )

    def execute_reallocation(self):
        """Execute reallocation using the new modular bot."""
        return self.bot.run()


def lambda_handler(event, context):
    """
    AWS Lambda handler function.

    Args:
        event: Lambda event data (from CloudWatch Events)
        context: Lambda context object

    Returns:
        dict: Response with status and details
    """
    try:
        logger.info(f"Lambda function started. Request ID: {context.aws_request_id}")
        logger.info(f"Remaining time: {context.get_remaining_time_in_millis()}ms")
        logger.info(f"Event: {json.dumps(event)}")

        # Check if we have enough time to execute (need at least 30 seconds)
        if context.get_remaining_time_in_millis() < 30000:
            logger.warning("Insufficient time remaining, skipping execution")
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'status': 'skipped',
                    'message': 'Insufficient time remaining for execution',
                    'transaction_hash': None
                }),
                'headers': {
                    'Content-Type': 'application/json'
                }
            }

        # Load cloud environment variables
        load_cloud_env()

        # Initialize and run bot
        bot = ReallocationBot()
        result = bot.execute_reallocation()

        logger.info(f"Reallocation result: {result}")
        logger.info(f"Execution completed. Remaining time: {context.get_remaining_time_in_millis()}ms")

        # Return proper Lambda response
        return {
            'statusCode': 200 if result['status'] == 'success' else 500,
            'body': json.dumps(result),
            'headers': {
                'Content-Type': 'application/json'
            }
        }

    except Exception as e:
        error_msg = f"Lambda handler failed: {str(e)}"
        logger.error(error_msg)
        logger.error(f"Traceback: {traceback.format_exc()}")

        return {
            'statusCode': 500,
            'body': json.dumps({
                'status': 'error',
                'message': error_msg,
                'transaction_hash': None
            }),
            'headers': {
                'Content-Type': 'application/json'
            }
        }


if __name__ == "__main__":
    # For local testing
    bot = ReallocationBot()
    result = bot.execute_reallocation()
    print(json.dumps(result, indent=2))
