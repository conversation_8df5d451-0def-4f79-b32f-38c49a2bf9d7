# LEGACY CONFIGURATION - MOVED TO terraform/main.tf
# This file is kept for backward compatibility during migration
# Please use the new modular configuration in terraform/main.tf

# For backward compatibility, this creates the OpenEden vault Lambda
# using the legacy configuration format

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }

  backend "s3" {
    bucket  = "morpho-reallocator-bot-terraform-state"
    key     = "legacy-terraform.tfstate"
    region  = "eu-central-1"
    encrypt = true
  }
}

provider "aws" {
  region = "eu-central-1"
}

# Legacy Lambda function for backward compatibility
resource "aws_lambda_function" "morpho_reallocator_bot_handler" {
  function_name = "morpho-reallocator-bot-handler"
  package_type  = "Image"
  role          = "arn:aws:iam::236840575805:role/lambda_exec_role"
  image_uri     = "236840575805.dkr.ecr.eu-central-1.amazonaws.com/morpho-reallocator-bot-handler:latest"
  architectures = ["x86_64"]
  timeout       = 300
  memory_size   = 1024

  tags = {
    Name        = "morpho-reallocator-bot-handler"
    Environment = "Production"
    Legacy      = "true"
  }

  environment {
    variables = {
      cloud_env = "morpho-reallocator-bot-handler"
    }
  }
}

# Legacy CloudWatch resources
resource "aws_cloudwatch_log_group" "morpho_reallocator_bot_log_group" {
  name              = "/aws/lambda/morpho-reallocator-bot-handler"
  retention_in_days = 14

  tags = {
    Name        = "Legacy Lambda Logs"
    Environment = "Production"
    Legacy      = "true"
  }
}

resource "aws_cloudwatch_event_rule" "schedule" {
  name                = "morpho-reallocator-bot-schedule"
  description         = "Trigger Legacy Lambda every hour"
  schedule_expression = "rate(1 hour)"

  tags = {
    Name        = "Legacy Lambda Schedule"
    Environment = "Production"
    Legacy      = "true"
  }

  state = "DISABLED"
}

resource "aws_cloudwatch_event_target" "lambda_target" {
  rule      = aws_cloudwatch_event_rule.schedule.name
  target_id = "morpho-reallocator-bot"
  arn       = aws_lambda_function.morpho_reallocator_bot_handler.arn
}

resource "aws_lambda_permission" "cloudwatch" {
  statement_id  = "AllowExecutionFromCloudWatch"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.morpho_reallocator_bot_handler.arn
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.schedule.arn
}

# Legacy monitoring and alerting
resource "aws_cloudwatch_log_metric_filter" "morpho_reallocator_bot_error_log_filter" {
  name           = "morpho-reallocator-bot-error-log-filter"
  pattern        = "\"ERROR\""
  log_group_name = aws_cloudwatch_log_group.morpho_reallocator_bot_log_group.name

  metric_transformation {
    name      = "ErrorOccurrences"
    namespace = "MorphoReallocatorBot/Legacy"
    value     = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "morpho_reallocator_bot_error_alarm" {
  alarm_name          = "morpho-reallocator-bot-error-alarm"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "ErrorOccurrences"
  namespace           = "MorphoReallocatorBot/Legacy"
  period              = 300
  statistic           = "Sum"
  threshold           = "1"
  alarm_description   = "Legacy Lambda error monitoring"
  actions_enabled     = true
  alarm_actions       = [aws_sns_topic.alerts.arn]

  tags = {
    Name        = "Legacy Lambda Error Alarm"
    Environment = "Production"
    Legacy      = "true"
  }
}

resource "aws_sns_topic" "alerts" {
  name = "morpho-reallocator-bot-alerts"

  tags = {
    Name        = "Legacy Alerts Topic"
    Environment = "Production"
    Legacy      = "true"
  }
}

resource "aws_sns_topic_subscription" "gasan_email" {
  topic_arn = aws_sns_topic.alerts.arn
  protocol  = "email"
  endpoint  = "<EMAIL>"
}

resource "aws_sns_topic_subscription" "colin_email" {
  topic_arn = aws_sns_topic.alerts.arn
  protocol  = "email"
  endpoint  = "<EMAIL>"
}

resource "aws_sns_topic_subscription" "jashiel_email" {
  topic_arn = aws_sns_topic.alerts.arn
  protocol  = "email"
  endpoint  = "<EMAIL>"
}

resource "aws_sns_topic_policy" "default" {
  arn    = aws_sns_topic.alerts.arn
  policy = data.aws_iam_policy_document.sns_policy.json
}

data "aws_iam_policy_document" "sns_policy" {
  statement {
    actions = ["SNS:Publish"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["cloudwatch.amazonaws.com"]
    }
    resources = [aws_sns_topic.alerts.arn]
  }
}

# Legacy ECR repository (kept for backward compatibility)
resource "aws_ecr_repository" "morpho_reallocator_bot_handler" {
  name = "morpho-reallocator-bot-handler"

  image_scanning_configuration {
    scan_on_push = true
  }

  encryption_configuration {
    encryption_type = "AES256"
  }

  tags = {
    Name        = "Legacy Morpho Reallocator Bot Handler"
    Environment = "Production"
    Legacy      = "true"
  }
}

# Output information about migration
output "migration_notice" {
  value = <<-EOT
    NOTICE: This is the legacy Terraform configuration.

    For new deployments, please use the modular configuration in terraform/main.tf

    To migrate:
    1. Deploy the new modular infrastructure: cd terraform && terraform init && terraform apply
    2. Update your CI/CD to use the new ECR repository
    3. Test the new Lambda functions
    4. Destroy this legacy infrastructure: terraform destroy
  EOT
}

output "legacy_lambda_function_name" {
  description = "Name of the legacy Lambda function"
  value       = aws_lambda_function.morpho_reallocator_bot_handler.function_name
}

output "legacy_ecr_repository_url" {
  description = "URL of the legacy ECR repository"
  value       = aws_ecr_repository.morpho_reallocator_bot_handler.repository_url
}
