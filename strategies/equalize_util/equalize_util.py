from typing import List, Optional, Any
from strategies.strategy import Strategy
from config.shared.global_config import get_min_utilization_delta_bips
from src.market_data import MarketData
from src.vault_data import VaultData
from utils.math import *

zero_address = "0x0000000000000000000000000000000000000000"
max_uint256 = 2**256 - 1

class EqualizeUtil(Strategy):
    """
    EqualizeUtil is a strategy that aims to equalize the utilization ratios across different markets.
    It adjusts the allocation of assets to achieve a balanced utilization ratio.
    """

    def find_reallocation(self, vault_data: VaultData) -> Optional[List[Any]]:
        markets_data: List[MarketData] = [
            market_data
            for market_data in vault_data.data["market_data"]
            if market_data.params["collateral_token"] != zero_address
        ]

        total_borrow_assets = sum(market_data.state["total_borrow_assets"] for market_data in markets_data)
        total_supply_assets = sum(market_data.state["total_supply_assets"] for market_data in markets_data)
        target_utilization = w_div_down(total_borrow_assets, total_supply_assets)

        total_withdrawable_amount = 0
        total_depositable_amount = 0

        did_exceed_min_utilization_delta = False

        for market_data in markets_data:
            utilization = get_utilization(market_data.state)

            if utilization > target_utilization:
                total_depositable_amount += get_depositable_amount(market_data, target_utilization)
            else:
                total_withdrawable_amount += get_withdrawable_amount(market_data, target_utilization)

            delta_bips = abs(int((utilization - target_utilization) // 1_000_000_000) / 1e5)
            if delta_bips > get_min_utilization_delta_bips(market_data.chain_id):
                did_exceed_min_utilization_delta = True

        to_reallocate = min(total_withdrawable_amount, total_depositable_amount)

        if to_reallocate == 0 or not did_exceed_min_utilization_delta:
            return None

        remaining_withdrawal = to_reallocate
        remaining_deposit = to_reallocate

        withdrawals = []
        deposits = []

        for market_data in markets_data:
            utilization = get_utilization(market_data.state)

            if utilization > target_utilization:
                deposit = min(
                    get_depositable_amount(market_data, target_utilization),
                    remaining_deposit,
                )
                remaining_deposit -= deposit

                deposits.append(
                    (
                        market_data.params,
                        max_uint256 if remaining_deposit == 0 else market_data.data['vault_assets'] + deposit,
                    )
                )
            else:
                withdrawal = min(
                    get_withdrawable_amount(market_data, target_utilization),
                    remaining_withdrawal,
                )
                remaining_withdrawal -= withdrawal

                withdrawals.append(
                    (
                        market_data.params,
                        market_data.data['vault_assets'] - withdrawal,
                    )
                )

            if remaining_withdrawal == 0 and remaining_deposit == 0:
                break

        return withdrawals + deposits
