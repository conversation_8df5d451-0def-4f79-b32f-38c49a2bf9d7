"""
DEPRECATED: This configuration file is being refactored.

Please use the new modular configuration system:
- config/shared/ for global settings
- config/vaults/ for vault-specific settings

This file is maintained for backward compatibility during migration.
"""

import os
import warnings
from log import get_logger

# Issue deprecation warning
warnings.warn(
    "config.config is deprecated. Use config.shared.global_config and vault-specific configs instead.",
    DeprecationWarning,
    stacklevel=2
)

logger = get_logger(__name__)

# Import from new configuration structure
try:
    from config.shared.global_config import *
    from config.vaults.openeden.config import *
except ImportError:
    # Fallback to legacy configuration for backward compatibility
    logger.warning("New configuration structure not found, using legacy configuration")

    from decimal import Decimal
    from typing import Dict, Optional
    import json
    from dotenv import load_dotenv
    load_dotenv()

    PRIMARY_CHAIN_ID = int(os.getenv("BASE_CHAIN_ID", 8453))
    PRIMARY_RPC_URL= os.getenv("RPC_URL_8453", "https://base-mainnet.g.alchemy.com/v2/fvtcFcF6yRLk1k9tjGaJk")

    WALLET_ADDRESS = os.getenv("WALLET_ADDRESS")
    FORDEFI_VAULT_ID = os.getenv("FORDEFI_VAULT_ID")

    # Strategy Configuration
    WAD = Decimal("1e18")
    MAX_UINT_256 = 115792089237316195423570985008687907853269984665640564039457584007913129639935
    CURVE_STEEPNESS = 4
    TARGET_UTILIZATION = Decimal("0.9") * WAD

    # Import ABIs from shared location
    try:
        from config.shared.abis.vault_abi import VAULT_ABI
        from config.shared.abis.morpho_abi import MORPHO_ABI, MORPHO_ADDRESS
    except ImportError:
        # Fallback to inline ABIs if shared ABIs not available
        VAULT_ABI = json.loads(
            '[{"inputs":[{"internalType":"address","name":"owner","type":"address"},{"internalType":"address","name":"morpho","type":"address"},{"internalType":"uint256","name":"initialTimelock","type":"uint256"},{"internalType":"address","name":"_asset","type":"address"},{"internalType":"string","name":"__name","type":"string"},{"internalType":"string","name":"__symbol","type":"string"}],"stateMutability":"nonpayable","type":"constructor"}]'
        )
        MORPHO_ADDRESS = "0xBBBBBbbBBb9cC5e90e3b3Af64bdAF62C37EEFFCb"
        MORPHO_ABI = json.loads(
            '[{"inputs":[{"internalType":"address","name":"newOwner","type":"address"}],"stateMutability":"nonpayable","type":"constructor"}]'
        )

    VAULT_ADDRESS = "******************************************"

    # Legacy market configuration for backward compatibility
    market_names = {
        "0x38c846197ac32a752a60c25d4536ebb0c3920c532e9a859c38c91efb7b8c2abb": "Idle",
        "0x9103c3b4e834476c9a62ea009ba2c884ee42e94e6e314a26f04d312434191836": "cbBTC",
    }

    priority_list = [
        "0x9103c3b4e834476c9a62ea009ba2c884ee42e94e6e314a26f04d312434191836",   # cbBTC
        "0x38c846197ac32a752a60c25d4536ebb0c3920c532e9a859c38c91efb7b8c2abb",   # Idle
    ]

    target_util = {
        "0x9103c3b4e834476c9a62ea009ba2c884ee42e94e6e314a26f04d312434191836": Decimal("0.95") * WAD,   # cbBTC
        "0x38c846197ac32a752a60c25d4536ebb0c3920c532e9a859c38c91efb7b8c2abb": Decimal("1.00") * WAD,   # Idle
    }

    vaults_min_utilization_delta_bips = {
        1: 50000,  # 0.5%
        8453: 50000,  # 0.5%
    }

    DEFAULT_MIN_UTILIZATION_DELTA_BIPS = 50000

DEFAULT_MIN_UTILIZATION_DELTA_BIPS = 50000  # 0.5%

market_names = {
    "0x38c846197ac32a752a60c25d4536ebb0c3920c532e9a859c38c91efb7b8c2abb": "Idle",
    "0x9103c3b4e834476c9a62ea009ba2c884ee42e94e6e314a26f04d312434191836": "cbBTC",
    "0x669b68ae003954dde5b2be025fa373bfc6b6134fc3180ea746c31e892243ae81": "PT-USR Sept",
    "0x45f3b5688e7ba25071f78d1ce51d1b893faa3c86897b12204cdff3af6b3611f8": "mBasis",
    "0x1478d70d1fde3fd5b8eb5766e82b03aa56a1df72a54fc8cd33aad666b0bd5008": "RLP",
    "0x04f7605a25699d0eff9f92908d25da8702ff59596d159e8bb66eca24b021d99f": "PT-USR April",
    "0x99f294c452edc091c988688d501dca78a06ba559065c242b19653452e6affc7a": "cUSDO",
    "0xff0f2bd52ca786a4f8149f96622885e880222d8bed12bbbf5950296be8d03f89": "USR",
    "0xb5b3fc38249c9a0aadf7ff0fcc48ef2eec49ef746b9b74a95597132c7a614116": "hyUSD",
    "0x2c10e050eb191f3e6ab9475a442d8d8b6a13e82dad7a9110475f4d22392a1a0c": "PT-cUSDO Jul",
    "0x1c21c59df9db44bf6f645d854ee710a8ca17b479451447e9f56758aee10a2fad": "cbETH",
    "0x5a24250884b607439e8eb2a5bf7e4f6647af665936f47d0a8509ff783b3916ec": "wstUSR"
}

priority_list = [
    "0x669b68ae003954dde5b2be025fa373bfc6b6134fc3180ea746c31e892243ae81",   # PT-USR Sept
    "0x1478d70d1fde3fd5b8eb5766e82b03aa56a1df72a54fc8cd33aad666b0bd5008",   # RLP
    "0x99f294c452edc091c988688d501dca78a06ba559065c242b19653452e6affc7a",   # cUSDO
    "0xff0f2bd52ca786a4f8149f96622885e880222d8bed12bbbf5950296be8d03f89",   # USR
    "0x5a24250884b607439e8eb2a5bf7e4f6647af665936f47d0a8509ff783b3916ec",   # wstUSR
    "0x45f3b5688e7ba25071f78d1ce51d1b893faa3c86897b12204cdff3af6b3611f8",   # mBASIS
    "0x1c21c59df9db44bf6f645d854ee710a8ca17b479451447e9f56758aee10a2fad",   # cbETH
    "0xb5b3fc38249c9a0aadf7ff0fcc48ef2eec49ef746b9b74a95597132c7a614116",   # hyUSD
    "0x2c10e050eb191f3e6ab9475a442d8d8b6a13e82dad7a9110475f4d22392a1a0c",   # PT-cUSDO Jul
    "0x9103c3b4e834476c9a62ea009ba2c884ee42e94e6e314a26f04d312434191836",   # cbBTC (considered as Idle market),
    "0x04f7605a25699d0eff9f92908d25da8702ff59596d159e8bb66eca24b021d99f",   # PT-USR April - do not allocate
    "0x38c846197ac32a752a60c25d4536ebb0c3920c532e9a859c38c91efb7b8c2abb",   # Idle - do not allocate
]
 
target_util = {
    # Mapping of market IDs to target utilization ratios
    "0x669b68ae003954dde5b2be025fa373bfc6b6134fc3180ea746c31e892243ae81": Decimal("0.91") * WAD,   # PT-USR Sept
    "0x1478d70d1fde3fd5b8eb5766e82b03aa56a1df72a54fc8cd33aad666b0bd5008": Decimal("0.92") * WAD,   # RLP
    "0x99f294c452edc091c988688d501dca78a06ba559065c242b19653452e6affc7a": Decimal("0.90") * WAD,   # cUSDO
    "0xff0f2bd52ca786a4f8149f96622885e880222d8bed12bbbf5950296be8d03f89": Decimal("0.90") * WAD,   # USR
    "0x5a24250884b607439e8eb2a5bf7e4f6647af665936f47d0a8509ff783b3916ec": Decimal("0.90") * WAD,   # wstUSR
    "0x45f3b5688e7ba25071f78d1ce51d1b893faa3c86897b12204cdff3af6b3611f8": Decimal("0.91") * WAD,   # mBASIS
    "0x1c21c59df9db44bf6f645d854ee710a8ca17b479451447e9f56758aee10a2fad": Decimal("0.94") * WAD,   # cbETH
    "0xb5b3fc38249c9a0aadf7ff0fcc48ef2eec49ef746b9b74a95597132c7a614116": Decimal("0.90") * WAD,   # hyUSD
    "0x2c10e050eb191f3e6ab9475a442d8d8b6a13e82dad7a9110475f4d22392a1a0c": Decimal("0.99") * WAD,   # PT-cUSDO Jul
    "0x9103c3b4e834476c9a62ea009ba2c884ee42e94e6e314a26f04d312434191836": Decimal("0.95") * WAD,   # cbBTC (considered as Idle market),
    "0x04f7605a25699d0eff9f92908d25da8702ff59596d159e8bb66eca24b021d99f": Decimal("1.00") * WAD,   # PT-USR April - do not allocate
    "0x38c846197ac32a752a60c25d4536ebb0c3920c532e9a859c38c91efb7b8c2abb": Decimal("1.00") * WAD,   # Idle - do not allocate
}

# Legacy configurations moved to new structure
# These are maintained for backward compatibility during migration

# TODO: Add more specific configurations for different strategies
# For example, PriorityUtil might need a list of prioritized markets
# or a target utilization ratio.
# This can be extended as needed based on the strategy's requirements.
