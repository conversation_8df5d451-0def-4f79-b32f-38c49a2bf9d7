"""
OpenEden vault-specific configuration.

This module contains all configuration specific to the OpenEden vault,
including vault address, market configurations, and strategy parameters.
"""

import os
from decimal import Decimal
from config.shared.global_config import WAD

# Vault Configuration
VAULT_NAME = "OpenEden"
VAULT_ADDRESS = "******************************************"
FORDEFI_VAULT_ID = os.getenv("FORDEFI_VAULT_ID")

# Chain Configuration
PRIMARY_CHAIN_ID = int(os.getenv("BASE_CHAIN_ID", 8453))

# Strategy Configuration
TARGET_UTILIZATION = Decimal("0.9") * WAD

# Market Configuration
market_names = {
    "0x38c846197ac32a752a60c25d4536ebb0c3920c532e9a859c38c91efb7b8c2abb": "Idle",
    "0x9103c3b4e834476c9a62ea009ba2c884ee42e94e6e314a26f04d312434191836": "cbBTC",
    "0x669b68ae003954dde5b2be025fa373bfc6b6134fc3180ea746c31e892243ae81": "PT-USR Sept",
    "0x45f3b5688e7ba25071f78d1ce51d1b893faa3c86897b12204cdff3af6b3611f8": "mBasis",
    "0x1478d70d1fde3fd5b8eb5766e82b03aa56a1df72a54fc8cd33aad666b0bd5008": "RLP",
    "0x04f7605a25699d0eff9f92908d25da8702ff59596d159e8bb66eca24b021d99f": "PT-USR April",
    "0x99f294c452edc091c988688d501dca78a06ba559065c242b19653452e6affc7a": "cUSDO",
    "0xff0f2bd52ca786a4f8149f96622885e880222d8bed12bbbf5950296be8d03f89": "USR",
    "0xb5b3fc38249c9a0aadf7ff0fcc48ef2eec49ef746b9b74a95597132c7a614116": "hyUSD",
    "0x2c10e050eb191f3e6ab9475a442d8d8b6a13e82dad7a9110475f4d22392a1a0c": "PT-cUSDO Jul",
    "0x1c21c59df9db44bf6f645d854ee710a8ca17b479451447e9f56758aee10a2fad": "cbETH",
    "0x5a24250884b607439e8eb2a5bf7e4f6647af665936f47d0a8509ff783b3916ec": "wstUSR"
}

priority_list = [
    "0x669b68ae003954dde5b2be025fa373bfc6b6134fc3180ea746c31e892243ae81",   # PT-USR Sept
    "0x1478d70d1fde3fd5b8eb5766e82b03aa56a1df72a54fc8cd33aad666b0bd5008",   # RLP
    "0x99f294c452edc091c988688d501dca78a06ba559065c242b19653452e6affc7a",   # cUSDO
    "0xff0f2bd52ca786a4f8149f96622885e880222d8bed12bbbf5950296be8d03f89",   # USR
    "0x5a24250884b607439e8eb2a5bf7e4f6647af665936f47d0a8509ff783b3916ec",   # wstUSR
    "0x45f3b5688e7ba25071f78d1ce51d1b893faa3c86897b12204cdff3af6b3611f8",   # mBASIS
    "0x1c21c59df9db44bf6f645d854ee710a8ca17b479451447e9f56758aee10a2fad",   # cbETH
    "0xb5b3fc38249c9a0aadf7ff0fcc48ef2eec49ef746b9b74a95597132c7a614116",   # hyUSD
    "0x2c10e050eb191f3e6ab9475a442d8d8b6a13e82dad7a9110475f4d22392a1a0c",   # PT-cUSDO Jul
    "0x9103c3b4e834476c9a62ea009ba2c884ee42e94e6e314a26f04d312434191836",   # cbBTC (considered as Idle market),
    "0x04f7605a25699d0eff9f92908d25da8702ff59596d159e8bb66eca24b021d99f",   # PT-USR April - do not allocate
    "0x38c846197ac32a752a60c25d4536ebb0c3920c532e9a859c38c91efb7b8c2abb",   # Idle - do not allocate
]
 
target_util = {
    # Mapping of market IDs to target utilization ratios
    "0x669b68ae003954dde5b2be025fa373bfc6b6134fc3180ea746c31e892243ae81": Decimal("0.91") * WAD,   # PT-USR Sept
    "0x1478d70d1fde3fd5b8eb5766e82b03aa56a1df72a54fc8cd33aad666b0bd5008": Decimal("0.92") * WAD,   # RLP
    "0x99f294c452edc091c988688d501dca78a06ba559065c242b19653452e6affc7a": Decimal("0.90") * WAD,   # cUSDO
    "0xff0f2bd52ca786a4f8149f96622885e880222d8bed12bbbf5950296be8d03f89": Decimal("0.90") * WAD,   # USR
    "0x5a24250884b607439e8eb2a5bf7e4f6647af665936f47d0a8509ff783b3916ec": Decimal("0.90") * WAD,   # wstUSR
    "0x45f3b5688e7ba25071f78d1ce51d1b893faa3c86897b12204cdff3af6b3611f8": Decimal("0.91") * WAD,   # mBASIS
    "0x1c21c59df9db44bf6f645d854ee710a8ca17b479451447e9f56758aee10a2fad": Decimal("0.94") * WAD,   # cbETH
    "0xb5b3fc38249c9a0aadf7ff0fcc48ef2eec49ef746b9b74a95597132c7a614116": Decimal("0.90") * WAD,   # hyUSD
    "0x2c10e050eb191f3e6ab9475a442d8d8b6a13e82dad7a9110475f4d22392a1a0c": Decimal("0.99") * WAD,   # PT-cUSDO Jul
    "0x9103c3b4e834476c9a62ea009ba2c884ee42e94e6e314a26f04d312434191836": Decimal("0.95") * WAD,   # cbBTC (considered as Idle market),
    "0x04f7605a25699d0eff9f92908d25da8702ff59596d159e8bb66eca24b021d99f": Decimal("1.00") * WAD,   # PT-USR April - do not allocate
    "0x38c846197ac32a752a60c25d4536ebb0c3920c532e9a859c38c91efb7b8c2abb": Decimal("1.00") * WAD,   # Idle - do not allocate
}

# Minimum utilization delta configuration
def get_min_utilization_delta_bips(chain_id: int) -> int:
    """
    Get minimum utilization delta in basis points for the given chain.

    Args:
        chain_id: Chain ID to get configuration for

    Returns:
        Minimum utilization delta in basis points
    """
    # OpenEden vault uses 0.5% (50000 bips) minimum delta for all chains
    return 50000

# Lambda Configuration
LAMBDA_FUNCTION_NAME = "morpho-reallocator-openeden"
LAMBDA_SCHEDULE = "rate(1 hour)"  # Run every hour
LAMBDA_TIMEOUT = 300  # 5 minutes
LAMBDA_MEMORY = 1024  # 1GB
