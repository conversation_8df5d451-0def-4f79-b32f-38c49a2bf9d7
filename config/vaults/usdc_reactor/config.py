"""
USDC Reactor vault-specific configuration.

This module contains all configuration specific to the USDC Reactor vault,
including vault address, market configurations, and strategy parameters.
"""

import os
from decimal import Decimal
from config.shared.global_config import WAD

# Vault Configuration
VAULT_NAME = "USDC Reactor"
VAULT_ADDRESS = ""
FORDEFI_VAULT_ID = os.getenv("FORDEFI_VAULT_ID")

# Chain Configuration
PRIMARY_CHAIN_ID = int(os.getenv("ETH_CHAIN_ID", 1))

# Strategy Configuration
TARGET_UTILIZATION = Decimal("0.9") * WAD

# Market Configuration
market_names = {
}

priority_list = [
]
 
target_util = {
    # Mapping of market IDs to target utilization ratios
    # "0x38c846197ac32a752a60c25d4536ebb0c3920c532e9a859c38c91efb7b8c2abb": Decimal("1.00") * WAD,   # Idle - do not allocate
}

# Minimum utilization delta configuration
def get_min_utilization_delta_bips(chain_id: int) -> int:
    """
    Get minimum utilization delta in basis points for the given chain.

    Args:
        chain_id: Chain ID to get configuration for

    Returns:
        Minimum utilization delta in basis points
    """
    # USDC Reactor vault uses 0.5% (50000 bips) minimum delta for all chains
    return 50000

# Lambda Configuration
LAMBDA_FUNCTION_NAME = "morpho-reallocator-usdc-reactor"
LAMBDA_SCHEDULE = "rate(4 hours)"  # Run every 4 hours
LAMBDA_TIMEOUT = 300  # 5 minutes
LAMBDA_MEMORY = 1024  # 1GB
