"""
Global configuration for the Morpho Reallocator Bot.

This module provides shared configuration settings that are used across all vaults:
- Environment variable loading from cloud_env
- Chain configuration
- Global constants
- Shared utilities
"""

import os
from log import get_logger
from decimal import Decimal
from typing import Dict, Optional
from dotenv import load_dotenv
from utils.load_cloud_env import load_cloud_env

# Load environment variables
load_dotenv()

# Load cloud environment on import
load_cloud_env()

logger = get_logger(__name__)

# Global Environment Variables
FORDEFI_BEARER_TOKEN = os.getenv("FORDEFI_BEARER_TOKEN")
FORDEFI_PRIVATE_KEY = os.getenv("FORDEFI_PRIVATE_KEY")
WALLET_ADDRESS = os.getenv("WALLET_ADDRESS")

# Chain Configuration
SUPPORTED_CHAINS = {
    8453: {  # Base
        "name": "Base",
        "rpc_url": os.getenv(
            "RPC_URL_8453",
            "https://base-mainnet.g.alchemy.com/v2/fvtcFcF6yRLk1k9tjGaJk",
        ),
    },
    1: {  # Ethereum Mainnet
        "name": "Ethereum",
        "rpc_url": os.getenv(
            "RPC_URL_1", "https://eth-mainnet.g.alchemy.com/v2/your_api_key"
        ),
    },
    42161: {  # Arbitrum
        "name": "Arbitrum",
        "rpc_url": os.getenv(
            "RPC_URL_42161", "https://arb-mainnet.g.alchemy.com/v2/your_api_key"
        ),
    },
}

# Global Constants
WAD = Decimal("1e18")
MAX_UINT_256 = (
    115792089237316195423570985008687907853269984665640564039457584007913129639935
)
CURVE_STEEPNESS = 4

# Default Configuration Values
DEFAULT_MIN_UTILIZATION_DELTA_BIPS = 50000  # 0.5%

# Minimum utilization delta configuration
vaults_min_utilization_delta_bips = {
    1: 50000,  # 0.5%
    8453: 50000,  # 0.5%
}


def get_min_utilization_delta_bips(chain_id: int) -> int:
    """Get minimum utilization delta for a specific chain."""
    return vaults_min_utilization_delta_bips.get(
        chain_id, DEFAULT_MIN_UTILIZATION_DELTA_BIPS
    )


def get_chain_config(chain_id: int) -> Optional[Dict]:
    """Get configuration for a specific chain."""
    return SUPPORTED_CHAINS.get(chain_id)


def get_rpc_url(chain_id: int) -> Optional[str]:
    """Get RPC URL for a specific chain."""
    chain_config = get_chain_config(chain_id)
    return chain_config["rpc_url"] if chain_config else None


# Vault Configuration Registry
# Maps vault names to their configuration module paths
VAULT_CONFIG_REGISTRY = {
    "openeden": "config.vaults.openeden.config",
    # Add more vaults here as they are created
    # "example_vault": "config.vaults.example_vault.config",
}


def get_vault_config_module(vault_name: str) -> Optional[str]:
    """Get the configuration module path for a specific vault."""
    return VAULT_CONFIG_REGISTRY.get(vault_name.lower())


def validate_global_config():
    """Validate that required global configuration is present."""
    required_vars = ["WALLET_ADDRESS"]
    missing_vars = [var for var in required_vars if not globals().get(var)]

    if missing_vars:
        logger.error(f"Missing required global configuration: {missing_vars}")
        raise ValueError(f"Missing required global configuration: {missing_vars}")

    logger.info("Global configuration validation passed")


# Validate configuration on import
try:
    validate_global_config()
except ValueError as e:
    logger.warning(f"Global configuration validation failed: {e}")
