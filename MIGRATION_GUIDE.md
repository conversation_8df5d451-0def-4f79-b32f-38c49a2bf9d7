# Migration Guide: Modular Architecture

This guide explains how to migrate from the legacy single-vault architecture to the new modular multi-vault architecture.

## Overview of Changes

### Before (Legacy Architecture)
- Single monolithic configuration in `config/config.py`
- Single Lambda function handling one vault
- GraphQL-based data fetching
- Hardcoded vault-specific logic

### After (Modular Architecture)
- Separated shared and vault-specific configurations
- Modular vault implementations with factory pattern
- Web3-based data fetching (no GraphQL dependency)
- Easy addition of new vaults without code duplication

## Architecture Changes

### Configuration Structure
```
# Before
config/
└── config.py                    # Everything in one file

# After
config/
├── shared/
│   ├── global_config.py         # Global constants and chain configs
│   └── abis/
│       ├── vault_abi.py         # Shared vault ABI
│       └── morpho_abi.py        # Shared Morpho ABI
└── vaults/
    └── openeden/
        └── config.py            # OpenEden-specific configuration
```

### Source Code Structure
```
# Before
src/
├── bot.py                       # Monolithic bot
├── market_data.py              # GraphQL-based
├── vault_data.py               # GraphQL-based
└── graph_client/               # GraphQL client code

# After
src/
├── bot.py                      # Modular bot + legacy compatibility
├── market_data.py              # Web3-based
├── vault_data.py               # Web3-based
└── vaults/
    ├── base_vault.py           # Base vault interface
    ├── vault_factory.py        # Vault factory pattern
    └── openeden/
        ├── vault.py            # OpenEden implementation
        └── lambda_handler.py   # OpenEden Lambda handler
```

### Infrastructure Structure
```
# Before
main.tf                         # Single Lambda function

# After
terraform/
├── main.tf                     # Modular infrastructure
└── modules/
    └── vault_lambda/           # Reusable Lambda module
        ├── main.tf
        ├── variables.tf
        └── outputs.tf
```

## Migration Steps

### Step 1: Backup Current Setup
```bash
# Backup current configuration
cp -r config/ config_backup/
cp main.tf main_backup.tf

# Backup current Lambda function (optional)
aws lambda get-function --function-name morpho-reallocator-bot-handler > lambda_backup.json
```

### Step 2: Test New Architecture
```bash
# Run comprehensive tests
./scripts/test-modular.sh all

# Test specific components
./scripts/test-modular.sh structure
./scripts/test-modular.sh config
./scripts/test-modular.sh factory
./scripts/test-modular.sh bot
./scripts/test-modular.sh terraform
```

### Step 3: Deploy New Infrastructure
```bash
# Option A: Use deployment script
./scripts/deploy-modular.sh

# Option B: Manual deployment
cd terraform
terraform init
terraform plan
terraform apply
```

### Step 4: Update Environment Variables
The new architecture uses the same environment variables but with cleaner organization:

```bash
# Required (same as before)
FORDEFI_BEARER_TOKEN='your_token'
FORDEFI_PRIVATE_KEY='your_key'
WALLET_ADDRESS='your_address'
FORDEFI_VAULT_ID='your_vault_id'

# Chain configuration (same as before)
BASE_CHAIN_ID=8453
RPC_URL_8453='your_rpc_url'

# Optional: Additional chains
RPC_URL_1='ethereum_rpc_url'
RPC_URL_42161='arbitrum_rpc_url'
```

### Step 5: Verify New Deployment
```bash
# Check Lambda functions
aws lambda list-functions --query 'Functions[?contains(FunctionName, `morpho-reallocator`)]'

# Test OpenEden Lambda function
aws lambda invoke --function-name morpho-reallocator-openeden response.json
cat response.json

# Monitor logs
aws logs tail /aws/lambda/morpho-reallocator-openeden --follow
```

### Step 6: Clean Up Legacy Infrastructure (Optional)
```bash
# After verifying new infrastructure works
terraform destroy  # In root directory (legacy infrastructure)
```

## Adding New Vaults

### 1. Create Vault Configuration
```python
# config/vaults/new_vault/config.py
VAULT_NAME = "NewVault"
VAULT_ADDRESS = "0x..."
FORDEFI_VAULT_ID = os.getenv("FORDEFI_VAULT_ID_NEW")
PRIMARY_CHAIN_ID = 8453

market_names = {
    "0x...": "Market 1",
    "0x...": "Market 2",
}

priority_list = ["0x...", "0x..."]

target_util = {
    "0x...": Decimal("0.9") * WAD,
    "0x...": Decimal("0.8") * WAD,
}
```

### 2. Create Vault Implementation
```python
# src/vaults/new_vault/vault.py
from src.vaults.base_vault import BaseVault
from config.vaults.new_vault.config import *

class NewVault(BaseVault):
    def __init__(self):
        vault_config = {
            "VAULT_NAME": VAULT_NAME,
            "VAULT_ADDRESS": VAULT_ADDRESS,
            "PRIMARY_CHAIN_ID": PRIMARY_CHAIN_ID,
            "FORDEFI_VAULT_ID": FORDEFI_VAULT_ID,
        }
        super().__init__(vault_config)
    
    def get_market_names(self):
        return market_names.copy()
    
    # ... implement other required methods
```

### 3. Create Lambda Handler
```python
# src/vaults/new_vault/lambda_handler.py
from src.vaults.new_vault.vault import NewVault
from src.bot import MorphoReallocatorBot

def lambda_handler(event, context):
    vault = NewVault()
    bot = MorphoReallocatorBot(
        vault_address=vault.get_vault_address(),
        chain_id=vault.get_chain_id(),
        # ... other parameters
    )
    return bot.run()
```

### 4. Register in Factory
```python
# src/vaults/vault_factory.py
from src.vaults.new_vault.vault import NewVault

class VaultFactory:
    _vault_registry = {
        "openeden": OpenEdenVault,
        "new_vault": NewVault,  # Add here
    }
```

### 5. Add to Terraform
```hcl
# terraform/main.tf
locals {
  vaults = {
    openeden = { ... }
    new_vault = {
      function_name       = "morpho-reallocator-new-vault"
      schedule_expression = "rate(2 hours)"
      timeout            = 600
      memory_size        = 2048
      cloud_env_secret   = "morpho-reallocator-new-vault"
    }
  }
}
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all new configuration files are properly structured
2. **Missing Environment Variables**: Check AWS Secrets Manager configuration
3. **Lambda Timeouts**: Increase timeout in vault configuration
4. **RPC Connection Issues**: Verify RPC URLs in environment variables

### Rollback Procedure

If issues occur, you can rollback to the legacy system:

```bash
# 1. Restore legacy configuration
cp -r config_backup/* config/
cp main_backup.tf main.tf

# 2. Redeploy legacy infrastructure
terraform init
terraform apply

# 3. Update Lambda function if needed
aws lambda update-function-code \
  --function-name morpho-reallocator-bot-handler \
  --image-uri your-legacy-image-uri
```

## Benefits of New Architecture

1. **Modularity**: Easy to add new vaults without touching existing code
2. **Maintainability**: Clear separation of concerns
3. **Scalability**: Each vault can have independent schedules and resources
4. **Reliability**: Web3-based data fetching eliminates GraphQL dependencies
5. **Testability**: Comprehensive test suite for all components

## Support

For issues or questions:
1. Check the test results: `./scripts/test-modular.sh all`
2. Review logs: `aws logs tail /aws/lambda/function-name --follow`
3. Validate configuration: `terraform validate`
4. Consult the README files in `terraform/` and individual vault directories
