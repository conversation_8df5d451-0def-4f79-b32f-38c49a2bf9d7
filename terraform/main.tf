# Main Terraform configuration for Morpho Reallocator Bot
# Supports modular vault deployments

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }

  # Backend configuration for V2 state
  # Note: S3 bucket will be created during first deployment
  # After first apply, uncomment this backend configuration and run 'terraform init' again
  # backend "s3" {
  #   bucket  = "morpho-reallocator-bot-terraform-state-v2"
  #   key     = "terraform-v2.tfstate"
  #   region  = "eu-central-1"
  #   encrypt = true
  # }
}

provider "aws" {
  region = var.aws_region
}

# Variables
variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "eu-central-1"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "production-v2"
}

# Note: lambda_role_arn is now created by Terraform and referenced directly

variable "ecr_repository_url" {
  description = "ECR repository URL"
  type        = string
  default     = "236840575805.dkr.ecr.eu-central-1.amazonaws.com"
}

variable "alert_emails" {
  description = "List of email addresses for alerts"
  type        = list(string)
  default     = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>"
  ]
}

# Local values
locals {
  common_tags = {
    Project     = "MorphoReallocatorBot-V2"
    Environment = var.environment
    ManagedBy   = "Terraform"
    Version     = "v2"
  }

  # Vault configurations for v2 deployment
  # Currently configured for single vault testing (openeden-v2)
  # Uncomment additional vaults below for multi-vault testing
  vaults = {
    openeden-v2 = {
      function_name       = "morpho-reallocator-openeden-v2"
      schedule_expression = "rate(1 hour)"
      timeout            = 300
      memory_size        = 1024
      cloud_env_secret   = "morpho-reallocator-openeden-v2"
      lambda_handler     = "src.vaults.openeden.lambda_handler.lambda_handler"
    }

    # Uncomment the vault below to enable multi-vault testing
    usdc_reactor_v2 = {
      function_name       = "morpho-reallocator-usdc-reactor"
      schedule_expression = "rate(4 hours)"
      timeout            = 600
      memory_size        = 2048
      cloud_env_secret   = "morpho-reallocator-usdc-reactor"
      lambda_handler     = "src.vaults.second_vault.lambda_handler.lambda_handler"
    }

    # Template for additional vaults
    # new_vault_v2 = {
    #   function_name       = "morpho-reallocator-new-vault-v2"
    #   schedule_expression = "rate(3 hours)"
    #   timeout            = 300
    #   memory_size        = 1024
    #   cloud_env_secret   = "morpho-reallocator-new-vault-v2"
    #   lambda_handler     = "src.vaults.new_vault.lambda_handler.lambda_handler"
    # }
  }
}

# IAM role for Lambda execution v2
resource "aws_iam_role" "lambda_exec_role_v2" {
  name = "lambda_exec_role_v2"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(local.common_tags, {
    Name = "Lambda Execution Role V2"
  })
}

# IAM policy for CloudWatch Logs and ECR access
resource "aws_iam_role_policy" "lambda_exec_policy_v2" {
  name = "lambda_exec_policy_v2"
  role = aws_iam_role.lambda_exec_role_v2.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:PutLogEvents",
          "logs:CreateLogStream",
          "logs:CreateLogGroup",
          "ecr:ListImages",
          "ecr:GetRepositoryPolicy",
          "ecr:GetDownloadUrlForLayer",
          "ecr:GetAuthorizationToken",
          "ecr:DescribeRepositories",
          "ecr:DescribeImages",
          "ecr:BatchGetImage",
          "ecr:BatchCheckLayerAvailability"
        ]
        Resource = "*"
      }
    ]
  })
}

# Attach AWS managed policies to the V2 role
resource "aws_iam_role_policy_attachment" "lambda_exec_secrets_manager_v2" {
  role       = aws_iam_role.lambda_exec_role_v2.name
  policy_arn = "arn:aws:iam::aws:policy/SecretsManagerReadWrite"
}

resource "aws_iam_role_policy_attachment" "lambda_exec_s3_access_v2" {
  role       = aws_iam_role.lambda_exec_role_v2.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonS3FullAccess"
}

# Attach custom policies that exist in the legacy role
resource "aws_iam_role_policy_attachment" "lambda_exec_s3_custom_v2" {
  role       = aws_iam_role.lambda_exec_role_v2.name
  policy_arn = "arn:aws:iam::236840575805:policy/s3_access_policy"
}

resource "aws_iam_role_policy_attachment" "lambda_exec_vpc_v2" {
  role       = aws_iam_role.lambda_exec_role_v2.name
  policy_arn = "arn:aws:iam::236840575805:policy/lambda_vpc_policy"
}

# S3 bucket for Terraform state v2 (if not exists)
resource "aws_s3_bucket" "terraform_state_v2" {
  bucket = "morpho-reallocator-bot-terraform-state-v2"

  tags = merge(local.common_tags, {
    Name = "Terraform State Bucket V2"
  })
}

resource "aws_s3_bucket_versioning" "terraform_state_versioning_v2" {
  bucket = aws_s3_bucket.terraform_state_v2.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "terraform_state_encryption_v2" {
  bucket = aws_s3_bucket.terraform_state_v2.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# ECR repository for Docker images v2
resource "aws_ecr_repository" "morpho_reallocator_bot_v2" {
  name = "morpho-reallocator-bot-v2"

  image_scanning_configuration {
    scan_on_push = true
  }

  encryption_configuration {
    encryption_type = "AES256"
  }

  tags = merge(local.common_tags, {
    Name = "Morpho Reallocator Bot ECR Repository V2"
  })
}

# SNS topic for alerts v2
resource "aws_sns_topic" "alerts_v2" {
  name = "morpho-reallocator-bot-alerts-v2"

  tags = merge(local.common_tags, {
    Name = "Morpho Reallocator Bot Alerts V2"
  })
}

# SNS topic subscriptions for email alerts v2
resource "aws_sns_topic_subscription" "email_alerts_v2" {
  for_each = toset(var.alert_emails)

  topic_arn = aws_sns_topic.alerts_v2.arn
  protocol  = "email"
  endpoint  = each.value
}

# SNS topic policy v2
resource "aws_sns_topic_policy" "alerts_policy_v2" {
  arn    = aws_sns_topic.alerts_v2.arn
  policy = data.aws_iam_policy_document.sns_policy_v2.json
}

data "aws_iam_policy_document" "sns_policy_v2" {
  statement {
    actions = ["SNS:Publish"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["cloudwatch.amazonaws.com"]
    }
    resources = [aws_sns_topic.alerts_v2.arn]
  }
}

# Deploy vault Lambda functions using the module v2
module "vault_lambdas_v2" {
  source = "./modules/vault_lambda"

  for_each = local.vaults

  vault_name              = each.key
  function_name          = each.value.function_name
  schedule_expression    = each.value.schedule_expression
  timeout               = each.value.timeout
  memory_size           = each.value.memory_size
  image_uri             = "${var.ecr_repository_url}/morpho-reallocator-bot-handler:latest"
  lambda_role_arn       = aws_iam_role.lambda_exec_role_v2.arn
  cloud_env_secret_name = each.value.cloud_env_secret
  sns_topic_arn         = aws_sns_topic.alerts_v2.arn
  lambda_handler        = each.value.lambda_handler

  tags = merge(local.common_tags, {
    VaultName = each.key
  })
}

# Outputs for v2 infrastructure
output "ecr_repository_url_v2" {
  description = "ECR repository URL for v2"
  value       = aws_ecr_repository.morpho_reallocator_bot_v2.repository_url
}

output "sns_topic_arn_v2" {
  description = "SNS topic ARN for alerts v2"
  value       = aws_sns_topic.alerts_v2.arn
}

output "vault_lambda_functions_v2" {
  description = "Information about deployed vault Lambda functions v2"
  value = {
    for vault_name, vault_config in local.vaults : vault_name => {
      function_name = module.vault_lambdas_v2[vault_name].lambda_function_name
      function_arn  = module.vault_lambdas_v2[vault_name].lambda_function_arn
      log_group     = module.vault_lambdas_v2[vault_name].log_group_name
      schedule_rule = module.vault_lambdas_v2[vault_name].schedule_rule_name
    }
  }
}
