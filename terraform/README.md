# Morpho Reallocator Bot - V2 Modular Terraform Infrastructure

This directory contains the **V2** modular Terraform configuration for deploying the Morpho Reallocator Bot infrastructure that supports multiple vaults. This V2 infrastructure is designed to run in parallel with the legacy system for safe testing and validation.

## Architecture Overview

The V2 modular architecture supports:
- **Parallel Deployment**: Completely isolated from legacy infrastructure using "-v2" naming convention
- **Multiple Vault Deployments**: Each vault gets its own Lambda function with specific configuration
- **Shared Resources**: ECR repository, SNS alerts, and monitoring shared across all vaults
- **Modular Design**: Easy to add new vaults without duplicating infrastructure code
- **Independent Scheduling**: Each vault can have its own execution schedule
- **Safe Testing**: No conflicts with existing production infrastructure

## Directory Structure

```
terraform/
├── main.tf                    # Main Terraform configuration
├── modules/
│   └── vault_lambda/         # Reusable module for vault Lambda deployments
│       ├── main.tf           # Module resources
│       ├── variables.tf      # Module input variables
│       └── outputs.tf        # Module outputs
└── README.md                 # This file
```

## V2 Infrastructure Naming Convention

All V2 resources use a distinct naming convention to avoid conflicts with legacy infrastructure:

- **S3 Bucket**: `morpho-reallocator-bot-terraform-state-v2`
- **ECR Repository**: `morpho-reallocator-bot-v2`
- **SNS Topic**: `morpho-reallocator-bot-alerts-v2`
- **Lambda Functions**: `morpho-reallocator-{vault-name}-v2`
- **CloudWatch Namespace**: `MorphoReallocatorBotV2/{vault-name}`
- **IAM Role**: `lambda_exec_role_v2`

## Vault Configuration

Vaults are configured in the `locals.vaults` block in `main.tf`. Currently configured for single vault testing:

```hcl
vaults = {
  openeden-v2 = {
    function_name       = "morpho-reallocator-openeden-v2"
    schedule_expression = "rate(1 hour)"
    timeout            = 300
    memory_size        = 1024
    cloud_env_secret   = "morpho-reallocator-openeden-v2"
  }
  # Uncomment additional vaults for multi-vault testing...
}
```

## Adding a New Vault for Multi-Vault Testing

To add a new vault to the V2 infrastructure:

1. **Create vault configuration** in `config/vaults/new_vault/config.py`
2. **Create vault implementation** in `src/vaults/new_vault/vault.py`
3. **Create Lambda handler** in `src/vaults/new_vault/lambda_handler.py`
4. **Add vault to Terraform** in `terraform/main.tf` (note the "-v2" suffix):

```hcl
new_vault_v2 = {
  function_name       = "morpho-reallocator-new-vault-v2"
  schedule_expression = "rate(2 hours)"
  timeout            = 600
  memory_size        = 2048
  cloud_env_secret   = "morpho-reallocator-new-vault-v2"
}
```

5. **Create the corresponding AWS Secrets Manager secret** with name `morpho-reallocator-new-vault-v2`

6. **Deploy the infrastructure**:
```bash
cd terraform
terraform plan
terraform apply
```

## Enabling Multi-Vault Testing

To enable multi-vault testing, simply uncomment the `second_vault_v2` configuration in `main.tf` and run `terraform apply`.

## V2 Deployment (Parallel Testing Environment)

### Prerequisites

- AWS CLI configured with appropriate credentials
- Terraform >= 1.0 installed
- Docker installed for building images
- Ensure IAM role `lambda_exec_role_v2` exists (or update the variable in `main.tf`)

### Important: V2 Infrastructure Setup

Before deploying, ensure you have:
1. **Created the V2 IAM role**: `lambda_exec_role_v2` with appropriate permissions
2. **Created AWS Secrets Manager secrets** for each vault (e.g., `morpho-reallocator-openeden-v2`)

### Manual V2 Deployment

1. **Build and push Docker image to V2 ECR repository**:
```bash
# Login to ECR
aws ecr get-login-password --region eu-central-1 | docker login --username AWS --password-stdin 236840575805.dkr.ecr.eu-central-1.amazonaws.com

# Build and push to V2 repository
docker build -t morpho-reallocator-bot-v2:latest .
docker tag morpho-reallocator-bot-v2:latest 236840575805.dkr.ecr.eu-central-1.amazonaws.com/morpho-reallocator-bot-v2:latest
docker push 236840575805.dkr.ecr.eu-central-1.amazonaws.com/morpho-reallocator-bot-v2:latest
```

2. **Deploy V2 Terraform infrastructure**:
```bash
cd terraform
terraform init
terraform plan
terraform apply
```

### Parallel Testing Benefits

- **Zero Downtime**: Legacy system continues running while V2 is tested
- **Safe Rollback**: Can destroy V2 infrastructure without affecting production
- **Resource Isolation**: Completely separate AWS resources prevent conflicts
- **Independent Monitoring**: Separate CloudWatch namespaces and SNS topics

## V2 Configuration Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `aws_region` | AWS region for deployment | `eu-central-1` |
| `environment` | Environment name | `production-v2` |
| `lambda_role_arn` | IAM role for Lambda execution | `lambda_exec_role_v2` |
| `ecr_repository_url` | ECR repository URL | Pre-configured URL |
| `alert_emails` | Email addresses for alerts | Team emails |

## Monitoring and Alerting

Each vault Lambda function includes:

- **CloudWatch Logs**: Centralized logging with 14-day retention
- **Error Monitoring**: Automatic error detection and alerting
- **Success Tracking**: Monitoring of successful executions
- **Missing Execution Alerts**: Alerts when executions don't occur as expected
- **SNS Notifications**: Email alerts to the team

## V2 Secrets Management

Environment variables are stored in AWS Secrets Manager with V2 naming:
- Each vault has its own secret (e.g., `morpho-reallocator-openeden-v2`)
- Secrets are automatically loaded by the Lambda functions
- Use the `cloud_env` environment variable to specify the secret name
- **Important**: Create V2 secrets before deploying infrastructure

## Migration Strategy from Legacy Infrastructure

The V2 infrastructure runs in parallel with legacy infrastructure for safe migration:

### Phase 1: Deploy V2 Infrastructure
1. **Deploy V2 infrastructure**: `cd terraform && terraform apply`
2. **Test V2 Lambda functions**: Verify they work correctly with single vault
3. **Monitor V2 metrics**: Ensure proper functionality in CloudWatch

### Phase 2: Multi-Vault Testing
1. **Enable second vault**: Uncomment `second_vault_v2` in `main.tf`
2. **Test multi-vault functionality**: Verify independent operation
3. **Performance testing**: Ensure no resource conflicts

### Phase 3: Production Migration (Future)
1. **Update CI/CD pipelines**: Point to V2 ECR repository
2. **Gradual traffic migration**: Move vaults one by one
3. **Destroy legacy infrastructure**: Only after full validation

## Troubleshooting

### Common Issues

1. **ECR Permission Denied**: Ensure AWS credentials have ECR access
2. **Lambda Timeout**: Increase timeout in vault configuration
3. **Missing Environment Variables**: Check AWS Secrets Manager configuration
4. **Schedule Not Working**: Verify CloudWatch Events permissions

### Useful V2 Commands

```bash
# Check V2 Lambda function logs
aws logs tail /aws/lambda/morpho-reallocator-openeden-v2 --follow

# Test V2 Lambda function
aws lambda invoke --function-name morpho-reallocator-openeden-v2 response.json

# Check V2 Terraform state
terraform show

# Validate V2 Terraform configuration
terraform validate

# Compare V2 vs Legacy resources
aws lambda list-functions --query 'Functions[?contains(FunctionName, `morpho-reallocator`)].[FunctionName]' --output table

# Monitor V2 CloudWatch metrics
aws cloudwatch get-metric-statistics --namespace MorphoReallocatorBotV2/openeden-v2 --metric-name ErrorOccurrences --start-time 2024-01-01T00:00:00Z --end-time 2024-01-02T00:00:00Z --period 3600 --statistics Sum
```

## Security Considerations

- **IAM Roles**: Lambda functions use least-privilege IAM roles
- **Encryption**: All resources use encryption at rest
- **VPC**: Consider deploying Lambda functions in VPC for additional security
- **Secrets**: Environment variables stored securely in AWS Secrets Manager

## Cost Optimization

- **Memory Sizing**: Adjust memory based on actual usage
- **Execution Frequency**: Optimize schedules based on vault requirements
- **Log Retention**: Logs retained for 14 days to balance cost and debugging needs
- **Reserved Capacity**: Consider reserved capacity for predictable workloads
