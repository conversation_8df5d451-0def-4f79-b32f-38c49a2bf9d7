# Outputs for the vault Lambda module

output "lambda_function_arn" {
  description = "ARN of the Lambda function"
  value       = aws_lambda_function.vault_lambda.arn
}

output "lambda_function_name" {
  description = "Name of the Lambda function"
  value       = aws_lambda_function.vault_lambda.function_name
}

output "log_group_name" {
  description = "Name of the CloudWatch log group"
  value       = aws_cloudwatch_log_group.vault_lambda_logs.name
}

output "schedule_rule_name" {
  description = "Name of the CloudWatch Events rule"
  value       = aws_cloudwatch_event_rule.vault_schedule.name
}

output "error_alarm_name" {
  description = "Name of the error alarm"
  value       = aws_cloudwatch_metric_alarm.vault_error_alarm.alarm_name
}

output "missing_execution_alarm_name" {
  description = "Name of the missing execution alarm"
  value       = aws_cloudwatch_metric_alarm.vault_missing_execution_alarm.alarm_name
}
