# Variables for the vault Lambda module

variable "vault_name" {
  description = "Name of the vault (e.g., 'openeden')"
  type        = string
}

variable "function_name" {
  description = "Name of the Lambda function"
  type        = string
}

variable "schedule_expression" {
  description = "CloudWatch Events schedule expression"
  type        = string
  default     = "rate(1 hour)"
}

variable "timeout" {
  description = "Lambda function timeout in seconds"
  type        = number
  default     = 300
}

variable "memory_size" {
  description = "Lambda function memory size in MB"
  type        = number
  default     = 1024
}

variable "image_uri" {
  description = "ECR image URI for the Lambda function"
  type        = string
}

variable "lambda_handler" {
  description = "Lambda handler entry point for the vault"
  type        = string
}

variable "lambda_role_arn" {
  description = "IAM role ARN for Lambda execution"
  type        = string
}

variable "cloud_env_secret_name" {
  description = "Name of the AWS Secrets Manager secret containing environment variables"
  type        = string
}

variable "sns_topic_arn" {
  description = "SNS topic ARN for alerts"
  type        = string
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}
