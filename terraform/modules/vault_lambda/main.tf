# Terraform module for deploying a vault-specific Lambda function

# Lambda function
resource "aws_lambda_function" "vault_lambda" {
  function_name = var.function_name
  package_type  = "Image"
  role          = var.lambda_role_arn
  image_uri     = var.image_uri
  architectures = ["x86_64"]
  timeout       = var.timeout
  memory_size   = var.memory_size

  # Override the Docker CMD with vault-specific handler
  image_config {
    command = [var.lambda_handler]
  }

  environment {
    variables = {
      cloud_env = var.cloud_env_secret_name
      VAULT_NAME = var.vault_name
    }
  }

  tags = merge(var.tags, {
    VaultName = var.vault_name
    Component = "Lambda"
  })
}

# CloudWatch Log Group
resource "aws_cloudwatch_log_group" "vault_lambda_logs" {
  name              = "/aws/lambda/${var.function_name}"
  retention_in_days = 14

  tags = merge(var.tags, {
    VaultName = var.vault_name
    Component = "Logs"
  })
}

# CloudWatch Event Rule for scheduling
resource "aws_cloudwatch_event_rule" "vault_schedule" {
  name                = "${var.function_name}-schedule"
  description         = "Trigger ${var.vault_name} vault Lambda function"
  schedule_expression = var.schedule_expression

  tags = merge(var.tags, {
    VaultName = var.vault_name
    Component = "Schedule"
  })
}

# CloudWatch Event Target
resource "aws_cloudwatch_event_target" "vault_lambda_target" {
  rule      = aws_cloudwatch_event_rule.vault_schedule.name
  target_id = "${var.vault_name}-lambda-target"
  arn       = aws_lambda_function.vault_lambda.arn
}

# Lambda permission for CloudWatch Events
resource "aws_lambda_permission" "vault_cloudwatch_permission" {
  statement_id  = "AllowExecutionFromCloudWatch-${var.vault_name}"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.vault_lambda.arn
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.vault_schedule.arn
}

# CloudWatch Log Metric Filter for errors
resource "aws_cloudwatch_log_metric_filter" "vault_error_filter" {
  name           = "${var.function_name}-error-filter"
  pattern        = "\"ERROR\""
  log_group_name = aws_cloudwatch_log_group.vault_lambda_logs.name

  metric_transformation {
    name      = "ErrorOccurrences"
    namespace = "MorphoReallocatorBotV2/${var.vault_name}"
    value     = "1"
  }
}

# CloudWatch Metric Alarm for errors
resource "aws_cloudwatch_metric_alarm" "vault_error_alarm" {
  alarm_name          = "${var.function_name}-error-alarm"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "ErrorOccurrences"
  namespace           = "MorphoReallocatorBotV2/${var.vault_name}"
  period              = 300
  statistic           = "Sum"
  threshold           = "1"
  alarm_description   = "Error alarm for ${var.vault_name} vault Lambda function"
  actions_enabled     = true
  alarm_actions       = [var.sns_topic_arn]

  tags = merge(var.tags, {
    VaultName = var.vault_name
    Component = "Alarm"
  })
}

# CloudWatch Log Metric Filter for successful executions
resource "aws_cloudwatch_log_metric_filter" "vault_success_filter" {
  name           = "${var.function_name}-success-filter"
  pattern        = "\"reallocation completed successfully\""
  log_group_name = aws_cloudwatch_log_group.vault_lambda_logs.name

  metric_transformation {
    name      = "SuccessfulExecutions"
    namespace = "MorphoReallocatorBotV2/${var.vault_name}"
    value     = "1"
  }
}

# CloudWatch Metric Alarm for missing executions (no success in 2 hours)
resource "aws_cloudwatch_metric_alarm" "vault_missing_execution_alarm" {
  alarm_name          = "${var.function_name}-missing-execution-alarm"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "SuccessfulExecutions"
  namespace           = "MorphoReallocatorBotV2/${var.vault_name}"
  period              = 7200  # 2 hours
  statistic           = "Sum"
  threshold           = "1"
  alarm_description   = "Missing execution alarm for ${var.vault_name} vault Lambda function"
  actions_enabled     = true
  alarm_actions       = [var.sns_topic_arn]
  treat_missing_data  = "breaching"

  tags = merge(var.tags, {
    VaultName = var.vault_name
    Component = "Alarm"
  })
}
