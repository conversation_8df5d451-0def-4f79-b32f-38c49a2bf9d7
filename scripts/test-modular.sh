#!/bin/bash

# Test script for the modular Morpho Reallocator Bot architecture
# This script runs various tests to validate the new modular implementation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Morpho Reallocator Bot - Modular Architecture Tests${NC}"
echo "======================================================="

# Function to run Python tests
run_python_tests() {
    echo -e "${YELLOW}🐍 Running Python unit tests...${NC}"
    
    if python3 -m pytest tests/test_modular_architecture.py -v; then
        echo -e "${GREEN}✅ Python unit tests passed${NC}"
        return 0
    else
        echo -e "${RED}❌ Python unit tests failed${NC}"
        return 1
    fi
}

# Function to test configuration loading
test_configuration() {
    echo -e "${YELLOW}⚙️  Testing configuration loading...${NC}"
    
    python3 -c "
import sys
import os
sys.path.append(os.getcwd())

try:
    # Test shared configuration
    from config.shared.global_config import WAD, SUPPORTED_CHAINS
    print('✅ Shared global configuration loaded')
    
    # Test vault configuration
    from config.vaults.openeden.config import VAULT_NAME, VAULT_ADDRESS
    print(f'✅ OpenEden vault configuration loaded: {VAULT_NAME}')
    
    # Test ABIs
    from config.shared.abis.vault_abi import VAULT_ABI
    from config.shared.abis.morpho_abi import MORPHO_ABI
    print('✅ ABIs loaded successfully')
    
    print('✅ All configuration tests passed')
except Exception as e:
    print(f'❌ Configuration test failed: {e}')
    sys.exit(1)
"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Configuration loading tests passed${NC}"
        return 0
    else
        echo -e "${RED}❌ Configuration loading tests failed${NC}"
        return 1
    fi
}

# Function to test vault factory
test_vault_factory() {
    echo -e "${YELLOW}🏭 Testing vault factory...${NC}"
    
    python3 -c "
import sys
import os
sys.path.append(os.getcwd())

try:
    from src.vaults.vault_factory import VaultFactory, list_vaults, create_vault_by_name
    
    # Test listing vaults
    vaults = list_vaults()
    print(f'✅ Available vaults: {vaults}')
    
    # Test creating OpenEden vault
    vault = create_vault_by_name('openeden')
    print(f'✅ Created vault: {vault.get_vault_name()} at {vault.get_vault_address()}')
    
    # Test validation
    is_valid = vault.validate_configuration()
    if is_valid:
        print('✅ Vault configuration validation passed')
    else:
        print('❌ Vault configuration validation failed')
        sys.exit(1)
    
    print('✅ All vault factory tests passed')
except Exception as e:
    print(f'❌ Vault factory test failed: {e}')
    sys.exit(1)
"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Vault factory tests passed${NC}"
        return 0
    else
        echo -e "${RED}❌ Vault factory tests failed${NC}"
        return 1
    fi
}

# Function to test Lambda handlers
test_lambda_handlers() {
    echo -e "${YELLOW}⚡ Testing Lambda handlers...${NC}"
    
    python3 -c "
import sys
import os
sys.path.append(os.getcwd())

try:
    # Test OpenEden Lambda handler local test
    from src.vaults.openeden.lambda_handler import local_test
    print('✅ OpenEden Lambda handler imported successfully')
    
    # Note: We don't run local_test() here as it requires actual AWS resources
    # In a real environment, you would run: local_test()
    
    print('✅ Lambda handler tests passed (import only)')
except Exception as e:
    print(f'❌ Lambda handler test failed: {e}')
    sys.exit(1)
"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Lambda handler tests passed${NC}"
        return 0
    else
        echo -e "${RED}❌ Lambda handler tests failed${NC}"
        return 1
    fi
}

# Function to test modular bot
test_modular_bot() {
    echo -e "${YELLOW}🤖 Testing modular bot...${NC}"

    python3 -c "
import sys
import os
sys.path.append(os.getcwd())

try:
    from src.bot import MorphoReallocatorBot, ReallocationBot
    from decimal import Decimal

    # Test modular bot initialization
    bot = MorphoReallocatorBot(
        vault_address='0x1234567890123456789012345678901234567890',
        chain_id=8453,
        market_names={'0xabc': 'Test Market'},
        priority_list=['0xabc'],
        target_util={'0xabc': Decimal('0.9') * Decimal('1e18')},
        min_utilization_delta_bips=50000,
        vault_name='openeden'
    )
    print('✅ Modular bot initialized successfully')

    # Test legacy compatibility bot
    legacy_bot = ReallocationBot()
    print('✅ Legacy compatibility bot initialized successfully')

    print('✅ All modular bot tests passed')
except Exception as e:
    print(f'❌ Modular bot test failed: {e}')
    sys.exit(1)
"

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Modular bot tests passed${NC}"
        return 0
    else
        echo -e "${RED}❌ Modular bot tests failed${NC}"
        return 1
    fi
}

# Function to test strategy refactoring
test_strategy() {
    echo -e "${YELLOW}⚡ Testing strategy refactoring...${NC}"

    python3 -c "
import sys
import os
sys.path.append(os.getcwd())

try:
    from strategies.priority_util.priority_util import PrioritizedUtilizationStrategy

    # Test strategy with vault name
    strategy = PrioritizedUtilizationStrategy(vault_name='openeden')
    print('✅ Strategy with vault name initialized successfully')

    # Test strategy without vault name (backward compatibility)
    strategy_default = PrioritizedUtilizationStrategy()
    print('✅ Strategy with default vault initialized successfully')

    # Test vault config registry
    from config.shared.global_config import get_vault_config_module
    config_path = get_vault_config_module('openeden')
    if config_path:
        print('✅ Vault config registry working correctly')
    else:
        print('❌ Vault config registry failed')
        sys.exit(1)

    print('✅ All strategy tests passed')
except Exception as e:
    print(f'❌ Strategy test failed: {e}')
    sys.exit(1)
"

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Strategy refactoring tests passed${NC}"
        return 0
    else
        echo -e "${RED}❌ Strategy refactoring tests failed${NC}"
        return 1
    fi
}

# Function to validate Terraform configuration
test_terraform() {
    echo -e "${YELLOW}🏗️  Testing Terraform configuration...${NC}"
    
    cd terraform
    
    # Initialize Terraform
    if terraform init -backend=false; then
        echo -e "${GREEN}✅ Terraform initialization successful${NC}"
    else
        echo -e "${RED}❌ Terraform initialization failed${NC}"
        cd ..
        return 1
    fi
    
    # Validate Terraform configuration
    if terraform validate; then
        echo -e "${GREEN}✅ Terraform configuration is valid${NC}"
    else
        echo -e "${RED}❌ Terraform configuration is invalid${NC}"
        cd ..
        return 1
    fi
    
    cd ..
    return 0
}

# Function to check file structure
test_file_structure() {
    echo -e "${YELLOW}📁 Testing file structure...${NC}"
    
    # Check required directories
    required_dirs=(
        "config/shared"
        "config/vaults/openeden"
        "src/vaults"
        "src/vaults/openeden"
        "terraform/modules/vault_lambda"
    )
    
    for dir in "${required_dirs[@]}"; do
        if [ -d "$dir" ]; then
            echo -e "${GREEN}✅ Directory exists: $dir${NC}"
        else
            echo -e "${RED}❌ Missing directory: $dir${NC}"
            return 1
        fi
    done
    
    # Check required files
    required_files=(
        "config/shared/global_config.py"
        "config/shared/abis/vault_abi.py"
        "config/shared/abis/morpho_abi.py"
        "config/vaults/openeden/config.py"
        "src/vaults/base_vault.py"
        "src/vaults/vault_factory.py"
        "src/vaults/openeden/vault.py"
        "src/vaults/openeden/lambda_handler.py"
        "terraform/main.tf"
        "terraform/modules/vault_lambda/main.tf"
    )
    
    for file in "${required_files[@]}"; do
        if [ -f "$file" ]; then
            echo -e "${GREEN}✅ File exists: $file${NC}"
        else
            echo -e "${RED}❌ Missing file: $file${NC}"
            return 1
        fi
    done
    
    echo -e "${GREEN}✅ File structure validation passed${NC}"
    return 0
}

# Main test execution
main() {
    local test_type="${1:-all}"
    local exit_code=0
    
    case "$test_type" in
        "structure")
            test_file_structure || exit_code=1
            ;;
        "config")
            test_configuration || exit_code=1
            ;;
        "factory")
            test_vault_factory || exit_code=1
            ;;
        "bot")
            test_modular_bot || exit_code=1
            ;;
        "strategy")
            test_strategy || exit_code=1
            ;;
        "lambda")
            test_lambda_handlers || exit_code=1
            ;;
        "terraform")
            test_terraform || exit_code=1
            ;;
        "python")
            run_python_tests || exit_code=1
            ;;
        "all")
            echo -e "${BLUE}Running all tests...${NC}"
            test_file_structure || exit_code=1
            test_configuration || exit_code=1
            test_vault_factory || exit_code=1
            test_modular_bot || exit_code=1
            test_strategy || exit_code=1
            test_lambda_handlers || exit_code=1
            test_terraform || exit_code=1
            # run_python_tests || exit_code=1  # Commented out as pytest might not be available
            ;;
        *)
            echo "Usage: $0 [structure|config|factory|bot|strategy|lambda|terraform|python|all]"
            echo ""
            echo "Test types:"
            echo "  structure  - Check file and directory structure"
            echo "  config     - Test configuration loading"
            echo "  factory    - Test vault factory functionality"
            echo "  bot        - Test modular bot implementation"
            echo "  strategy   - Test strategy refactoring"
            echo "  lambda     - Test Lambda handler implementations"
            echo "  terraform  - Validate Terraform configuration"
            echo "  python     - Run Python unit tests (requires pytest)"
            echo "  all        - Run all tests (default)"
            exit 1
            ;;
    esac
    
    if [ $exit_code -eq 0 ]; then
        echo -e "${GREEN}🎉 All tests passed successfully!${NC}"
    else
        echo -e "${RED}❌ Some tests failed${NC}"
    fi
    
    return $exit_code
}

# Run main function with all arguments
main "$@"
