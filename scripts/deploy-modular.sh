#!/bin/bash

# Deployment script for the V2 modular Morpho Reallocator Bot infrastructure
# This script helps deploy the new V2 modular architecture in parallel with legacy

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
AWS_REGION="eu-central-1"
ECR_REPOSITORY="morpho-reallocator-bot-v2"
TERRAFORM_DIR="terraform"
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo -e "${BLUE}🚀 Morpho Reallocator Bot - V2 Modular Deployment${NC}"
echo "===================================================="

# Ensure we're in the project root
cd "$PROJECT_ROOT"
echo -e "${BLUE}📁 Working directory: $(pwd)${NC}"

# Check prerequisites
echo -e "${YELLOW}📋 Checking prerequisites...${NC}"

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo -e "${RED}❌ AWS CLI is not installed. Please install it first.${NC}"
    exit 1
fi

# Check if Terraform is installed
if ! command -v terraform &> /dev/null; then
    echo -e "${RED}❌ Terraform is not installed. Please install it first.${NC}"
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker is not installed. Please install it first.${NC}"
    exit 1
fi

# Check AWS credentials
if ! aws sts get-caller-identity &> /dev/null; then
    echo -e "${RED}❌ AWS credentials not configured. Please run 'aws configure' first.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Function to build and push Docker image
build_and_push_image() {
    echo -e "${YELLOW}🐳 Building and pushing V2 Docker image...${NC}"

    # Check if Dockerfile exists
    if [ ! -f "Dockerfile" ]; then
        echo -e "${RED}❌ Dockerfile not found in $(pwd)${NC}"
        echo -e "${YELLOW}💡 Make sure you're running this script from the project root${NC}"
        exit 1
    fi

    # Get ECR login token
    echo -e "${BLUE}🔐 Logging into ECR...${NC}"
    aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin 236840575805.dkr.ecr.$AWS_REGION.amazonaws.com

    # Build the Docker image with correct platform for Lambda
    echo -e "${BLUE}🔨 Building Docker image for V2 repository...${NC}"
    docker build --platform linux/amd64 -t $ECR_REPOSITORY:latest .

    # Tag the image for ECR
    echo -e "${BLUE}🏷️  Tagging image for ECR...${NC}"
    docker tag $ECR_REPOSITORY:latest 236840575805.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPOSITORY:latest

    # Push the image
    echo -e "${BLUE}📤 Pushing image to V2 ECR repository...${NC}"
    docker push 236840575805.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPOSITORY:latest

    echo -e "${GREEN}✅ V2 Docker image built and pushed successfully${NC}"
    echo -e "${BLUE}📍 Image URI: 236840575805.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPOSITORY:latest${NC}"
}

# Function to deploy Terraform infrastructure
deploy_terraform() {
    echo -e "${YELLOW}🏗️  Deploying V2 Terraform infrastructure...${NC}"

    # Check if terraform directory exists
    if [ ! -d "$TERRAFORM_DIR" ]; then
        echo -e "${RED}❌ Terraform directory not found: $TERRAFORM_DIR${NC}"
        exit 1
    fi

    cd $TERRAFORM_DIR

    # Initialize Terraform
    echo -e "${BLUE}🔧 Initializing Terraform...${NC}"
    terraform init

    # Validate Terraform configuration
    echo -e "${BLUE}✅ Validating Terraform configuration...${NC}"
    terraform validate

    # Plan the deployment
    echo -e "${BLUE}📋 Terraform plan for V2 infrastructure:${NC}"
    terraform plan

    # Ask for confirmation
    echo -e "${YELLOW}❓ Do you want to apply these V2 infrastructure changes? (y/N)${NC}"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        # Apply the changes
        echo -e "${BLUE}🚀 Applying V2 infrastructure changes...${NC}"
        terraform apply -auto-approve
        echo -e "${GREEN}✅ V2 Terraform infrastructure deployed successfully${NC}"
    else
        echo -e "${YELLOW}⏸️  Deployment cancelled by user${NC}"
        cd ..
        exit 0
    fi

    cd ..
}

# Function to show deployment status
show_status() {
    echo -e "${BLUE}📊 V2 Deployment Status${NC}"
    echo "========================"

    if [ ! -d "$TERRAFORM_DIR" ]; then
        echo -e "${RED}❌ Terraform directory not found: $TERRAFORM_DIR${NC}"
        return 1
    fi

    cd $TERRAFORM_DIR

    # Check if Terraform state exists
    if ! terraform show &> /dev/null; then
        echo -e "${YELLOW}⚠️  No Terraform state found. Infrastructure may not be deployed yet.${NC}"
        cd ..
        return 1
    fi

    # Get V2 Terraform outputs
    echo -e "${YELLOW}V2 ECR Repository:${NC}"
    terraform output ecr_repository_url_v2 2>/dev/null || echo "Not available"

    echo -e "${YELLOW}V2 SNS Topic:${NC}"
    terraform output sns_topic_arn_v2 2>/dev/null || echo "Not available"

    echo -e "${YELLOW}V2 Vault Lambda Functions:${NC}"
    terraform output vault_lambda_functions_v2 2>/dev/null || echo "Not available"

    # Show Lambda function status
    echo ""
    echo -e "${BLUE}🔍 Lambda Function Status:${NC}"
    if command -v aws &> /dev/null; then
        aws lambda list-functions --query 'Functions[?contains(FunctionName, `morpho-reallocator`) && contains(FunctionName, `v2`)].[FunctionName,State,LastModified]' --output table 2>/dev/null || echo "Unable to fetch Lambda status"
    else
        echo "AWS CLI not available"
    fi

    cd ..
}

# Function to test Lambda function
test_lambda() {
    echo -e "${BLUE}🧪 Testing V2 Lambda Functions${NC}"
    echo "==============================="

    # Check Lambda function configuration first
    echo -e "${YELLOW}Checking Lambda function configuration...${NC}"
    aws lambda get-function --function-name morpho-reallocator-openeden-v2 --query 'Configuration.[FunctionName,ImageConfig.Command[0]]' --output table 2>/dev/null || echo "Unable to get function details"

    # Test openeden-v2 function
    echo -e "${YELLOW}Testing morpho-reallocator-openeden-v2...${NC}"
    if aws lambda invoke --function-name morpho-reallocator-openeden-v2 --payload '{}' response.json &> /dev/null; then
        echo -e "${GREEN}✅ Lambda function invoked successfully${NC}"
        echo -e "${BLUE}Response:${NC}"
        cat response.json | jq . 2>/dev/null || cat response.json
        rm -f response.json
    else
        echo -e "${RED}❌ Failed to invoke Lambda function${NC}"
        echo -e "${YELLOW}💡 Checking CloudWatch logs for errors...${NC}"

        # Get recent log events
        LOG_STREAM=$(aws logs describe-log-streams --log-group-name /aws/lambda/morpho-reallocator-openeden-v2 --order-by LastEventTime --descending --max-items 1 --query 'logStreams[0].logStreamName' --output text 2>/dev/null)
        if [ "$LOG_STREAM" != "None" ] && [ -n "$LOG_STREAM" ]; then
            echo -e "${BLUE}Recent log entries:${NC}"
            aws logs get-log-events --log-group-name /aws/lambda/morpho-reallocator-openeden-v2 --log-stream-name "$LOG_STREAM" --limit 5 --query 'events[*].message' --output text 2>/dev/null | tail -5
        fi
    fi

    echo ""
    echo -e "${BLUE}📊 Lambda Function Status:${NC}"
    aws lambda get-function --function-name morpho-reallocator-openeden-v2 --query 'Configuration.[FunctionName,State,LastModified,Runtime]' --output table 2>/dev/null || echo "Unable to get function details"

    # Check which handler is being used
    echo ""
    echo -e "${BLUE}🔍 Handler Analysis:${NC}"
    HANDLER_CMD=$(aws lambda get-function --function-name morpho-reallocator-openeden-v2 --query 'Configuration.ImageConfig.Command[0]' --output text 2>/dev/null)
    if [ "$HANDLER_CMD" = "None" ] || [ -z "$HANDLER_CMD" ]; then
        echo -e "${YELLOW}⚠️  Using default Dockerfile CMD (should be vault-specific handler)${NC}"
        echo -e "${BLUE}Expected: src.vaults.openeden.lambda_handler.lambda_handler${NC}"
        echo -e "${RED}Current: Default from Dockerfile${NC}"
    else
        echo -e "${GREEN}✅ Using custom handler: $HANDLER_CMD${NC}"
        if [[ "$HANDLER_CMD" == *"openeden"* ]]; then
            echo -e "${GREEN}✅ Correct vault-specific handler detected${NC}"
        else
            echo -e "${YELLOW}⚠️  Handler doesn't appear to be vault-specific${NC}"
        fi
    fi
}

# Function to validate modular architecture deployment
validate_architecture() {
    echo -e "${BLUE}🔍 Validating V2 Modular Architecture${NC}"
    echo "======================================"

    # Check if Lambda function exists
    if ! aws lambda get-function --function-name morpho-reallocator-openeden-v2 &> /dev/null; then
        echo -e "${RED}❌ V2 Lambda function not found${NC}"
        return 1
    fi

    # Check handler configuration
    HANDLER_CMD=$(aws lambda get-function --function-name morpho-reallocator-openeden-v2 --query 'Configuration.ImageConfig.Command[0]' --output text 2>/dev/null)

    echo -e "${BLUE}Handler Analysis:${NC}"
    if [ "$HANDLER_CMD" = "None" ] || [ -z "$HANDLER_CMD" ]; then
        echo -e "${RED}❌ Using legacy default handler${NC}"
        echo -e "${YELLOW}Expected: src.vaults.openeden.lambda_handler.lambda_handler${NC}"
        echo -e "${YELLOW}💡 Run 'terraform apply' to update handler configuration${NC}"
        return 1
    elif [[ "$HANDLER_CMD" == *"openeden"* ]]; then
        echo -e "${GREEN}✅ Using correct vault-specific handler: $HANDLER_CMD${NC}"
    else
        echo -e "${YELLOW}⚠️  Using handler: $HANDLER_CMD${NC}"
        echo -e "${YELLOW}Expected: src.vaults.openeden.lambda_handler.lambda_handler${NC}"
        return 1
    fi

    # Check environment variables
    echo -e "${BLUE}Environment Variables:${NC}"
    VAULT_NAME=$(aws lambda get-function --function-name morpho-reallocator-openeden-v2 --query 'Configuration.Environment.Variables.VAULT_NAME' --output text 2>/dev/null)
    if [ "$VAULT_NAME" = "openeden-v2" ]; then
        echo -e "${GREEN}✅ VAULT_NAME: $VAULT_NAME${NC}"
    else
        echo -e "${YELLOW}⚠️  VAULT_NAME: $VAULT_NAME (expected: openeden-v2)${NC}"
    fi

    # Test function execution
    echo -e "${BLUE}Function Execution Test:${NC}"
    if aws lambda invoke --function-name morpho-reallocator-openeden-v2 --payload '{}' /tmp/test_response.json &> /dev/null; then
        echo -e "${GREEN}✅ Function executes successfully${NC}"

        # Check if response indicates modular architecture
        if grep -q "vault_name" /tmp/test_response.json 2>/dev/null; then
            echo -e "${GREEN}✅ Response contains vault-specific data${NC}"
        else
            echo -e "${YELLOW}⚠️  Response doesn't contain expected vault-specific data${NC}"
        fi
        rm -f /tmp/test_response.json
    else
        echo -e "${RED}❌ Function execution failed${NC}"
        return 1
    fi

    echo -e "${GREEN}🎉 V2 Modular Architecture validation completed${NC}"
    return 0
}

# Main deployment flow
main() {
    case "${1:-all}" in
        "image")
            build_and_push_image
            ;;
        "terraform")
            deploy_terraform
            ;;
        "status")
            show_status
            ;;
        "test")
            test_lambda
            ;;
        "validate")
            validate_architecture
            ;;
        "all")
            build_and_push_image
            deploy_terraform
            show_status
            ;;
        *)
            echo "Usage: $0 [image|terraform|status|test|validate|all]"
            echo ""
            echo "Commands:"
            echo "  image     - Build and push V2 Docker image only"
            echo "  terraform - Deploy V2 Terraform infrastructure only"
            echo "  status    - Show V2 deployment status"
            echo "  test      - Test V2 Lambda functions"
            echo "  validate  - Validate V2 modular architecture deployment"
            echo "  all       - Build V2 image and deploy infrastructure (default)"
            echo ""
            echo "Examples:"
            echo "  $0 image      # Just update the Docker image"
            echo "  $0 terraform  # Just deploy infrastructure changes"
            echo "  $0 status     # Check current deployment status"
            echo "  $0            # Full deployment (image + infrastructure)"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"

echo -e "${GREEN}🎉 V2 Deployment completed successfully!${NC}"
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo "1. Test the V2 Lambda function: aws lambda invoke --function-name morpho-reallocator-openeden-v2 --payload '{}' response.json"
echo "2. Monitor V2 CloudWatch logs: aws logs tail /aws/lambda/morpho-reallocator-openeden-v2 --follow"
echo "3. Check V2 CloudWatch alarms and metrics"
echo "4. When ready, uncomment additional vaults in terraform/main.tf for multi-vault testing"
echo "5. V2 infrastructure runs in parallel with legacy - safe to test!"
