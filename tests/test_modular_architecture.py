"""
Tests for the modular architecture components.

This module tests the new modular vault architecture including:
- Vault factory functionality
- Base vault interface compliance
- OpenEden vault implementation
- Configuration loading and validation
"""

import unittest
import sys
import os
from unittest.mock import patch, MagicMock
from decimal import Decimal

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from log import get_logger

logger = get_logger(__name__)


class TestVaultFactory(unittest.TestCase):
    """Test the vault factory functionality."""
    
    def test_list_available_vaults(self):
        """Test listing available vault types."""
        try:
            from src.vaults.vault_factory import VaultFactory
            
            vaults = VaultFactory.list_available_vaults()
            self.assertIsInstance(vaults, list)
            self.assertIn("openeden", vaults)
            logger.info(f"✅ Available vaults: {vaults}")
            
        except Exception as e:
            logger.error(f"❌ Failed to list available vaults: {e}")
            raise
    
    def test_create_openeden_vault(self):
        """Test creating an OpenEden vault instance."""
        try:
            from src.vaults.vault_factory import VaultFactory
            
            vault = VaultFactory.create_vault("openeden")
            self.assertIsNotNone(vault)
            self.assertEqual(vault.get_vault_name(), "OpenEden")
            logger.info(f"✅ Created OpenEden vault: {vault.get_vault_address()}")
            
        except Exception as e:
            logger.error(f"❌ Failed to create OpenEden vault: {e}")
            raise
    
    def test_vault_configuration_validation(self):
        """Test vault configuration validation."""
        try:
            from src.vaults.vault_factory import VaultFactory
            
            vault = VaultFactory.create_vault("openeden")
            is_valid = vault.validate_configuration()
            self.assertTrue(is_valid)
            logger.info("✅ Vault configuration validation passed")
            
        except Exception as e:
            logger.error(f"❌ Vault configuration validation failed: {e}")
            raise


class TestBaseVault(unittest.TestCase):
    """Test the base vault interface."""
    
    def test_base_vault_interface(self):
        """Test that OpenEden vault implements all required methods."""
        try:
            from src.vaults.openeden.vault import OpenEdenVault
            from src.vaults.base_vault import BaseVault
            
            vault = OpenEdenVault()
            
            # Test that it's an instance of BaseVault
            self.assertIsInstance(vault, BaseVault)
            
            # Test required methods exist and return expected types
            market_names = vault.get_market_names()
            self.assertIsInstance(market_names, dict)
            self.assertGreater(len(market_names), 0)
            
            priority_list = vault.get_priority_list()
            self.assertIsInstance(priority_list, list)
            self.assertGreater(len(priority_list), 0)
            
            target_util = vault.get_target_utilization()
            self.assertIsInstance(target_util, dict)
            self.assertGreater(len(target_util), 0)
            
            min_delta = vault.get_min_utilization_delta_bips()
            self.assertIsInstance(min_delta, int)
            self.assertGreater(min_delta, 0)
            
            logger.info("✅ Base vault interface compliance test passed")
            
        except Exception as e:
            logger.error(f"❌ Base vault interface test failed: {e}")
            raise


class TestConfigurationStructure(unittest.TestCase):
    """Test the new configuration structure."""
    
    def test_shared_global_config(self):
        """Test loading shared global configuration."""
        try:
            from config.shared.global_config import (
                WAD, MAX_UINT_256, CURVE_STEEPNESS, 
                SUPPORTED_CHAINS, get_chain_config
            )
            
            # Test constants
            self.assertEqual(WAD, Decimal("1e18"))
            self.assertIsInstance(MAX_UINT_256, int)
            self.assertIsInstance(CURVE_STEEPNESS, int)
            
            # Test chain configuration
            self.assertIsInstance(SUPPORTED_CHAINS, dict)
            self.assertIn(8453, SUPPORTED_CHAINS)  # Base chain
            
            base_config = get_chain_config(8453)
            self.assertIsNotNone(base_config)
            self.assertEqual(base_config["name"], "Base")
            
            logger.info("✅ Shared global configuration test passed")
            
        except Exception as e:
            logger.error(f"❌ Shared global configuration test failed: {e}")
            raise
    
    def test_vault_specific_config(self):
        """Test loading vault-specific configuration."""
        try:
            from config.vaults.openeden.config import (
                VAULT_NAME, VAULT_ADDRESS, PRIMARY_CHAIN_ID,
                market_names, priority_list, target_util
            )
            
            # Test vault metadata
            self.assertEqual(VAULT_NAME, "OpenEden")
            self.assertIsInstance(VAULT_ADDRESS, str)
            self.assertIsInstance(PRIMARY_CHAIN_ID, int)
            
            # Test market configuration
            self.assertIsInstance(market_names, dict)
            self.assertIsInstance(priority_list, list)
            self.assertIsInstance(target_util, dict)
            
            # Test that all markets in priority list have target utilization
            for market_id in priority_list:
                self.assertIn(market_id, target_util)
            
            logger.info("✅ Vault-specific configuration test passed")
            
        except Exception as e:
            logger.error(f"❌ Vault-specific configuration test failed: {e}")
            raise
    
    def test_abi_loading(self):
        """Test loading shared ABIs."""
        try:
            from config.shared.abis.vault_abi import VAULT_ABI
            from config.shared.abis.morpho_abi import MORPHO_ABI, MORPHO_ADDRESS
            
            # Test that ABIs are loaded
            self.assertIsInstance(VAULT_ABI, list)
            self.assertIsInstance(MORPHO_ABI, list)
            self.assertIsInstance(MORPHO_ADDRESS, str)
            
            # Test that ABIs have content
            self.assertGreater(len(VAULT_ABI), 0)
            self.assertGreater(len(MORPHO_ABI), 0)
            
            logger.info("✅ ABI loading test passed")
            
        except Exception as e:
            logger.error(f"❌ ABI loading test failed: {e}")
            raise


class TestModularBot(unittest.TestCase):
    """Test the modular bot implementation."""
    
    @patch('src.bot.VaultData')
    @patch('src.bot.Web3Connector')
    @patch('src.bot.build_and_send_transaction')
    def test_modular_bot_initialization(self, mock_tx, mock_web3, mock_vault_data):
        """Test modular bot initialization with vault configuration."""
        try:
            from src.bot import MorphoReallocatorBot
            from decimal import Decimal
            
            # Mock configuration
            vault_address = "0x1234567890123456789012345678901234567890"
            chain_id = 8453
            market_names = {"0xabc": "Test Market"}
            priority_list = ["0xabc"]
            target_util = {"0xabc": Decimal("0.9") * Decimal("1e18")}
            min_delta = 50000
            
            # Create bot instance
            bot = MorphoReallocatorBot(
                vault_address=vault_address,
                chain_id=chain_id,
                market_names=market_names,
                priority_list=priority_list,
                target_util=target_util,
                min_utilization_delta_bips=min_delta
            )
            
            # Test that bot is initialized correctly
            self.assertEqual(bot.vault_address, vault_address)
            self.assertEqual(bot.chain_id, chain_id)
            self.assertEqual(bot.market_names, market_names)
            self.assertEqual(bot.priority_list, priority_list)
            self.assertEqual(bot.target_util, target_util)
            self.assertEqual(bot.min_utilization_delta_bips, min_delta)
            
            logger.info("✅ Modular bot initialization test passed")
            
        except Exception as e:
            logger.error(f"❌ Modular bot initialization test failed: {e}")
            raise


class TestLambdaHandlers(unittest.TestCase):
    """Test Lambda handler implementations."""
    
    @patch('src.vaults.openeden.lambda_handler.create_openeden_vault')
    @patch('src.vaults.openeden.lambda_handler.MorphoReallocatorBot')
    def test_openeden_lambda_handler(self, mock_bot, mock_vault):
        """Test OpenEden Lambda handler."""
        try:
            from src.vaults.openeden.lambda_handler import lambda_handler
            
            # Mock vault and bot
            mock_vault_instance = MagicMock()
            mock_vault_instance.get_vault_name.return_value = "OpenEden"
            mock_vault_instance.get_vault_address.return_value = "0x1234"
            mock_vault_instance.get_chain_id.return_value = 8453
            mock_vault_instance.get_market_names.return_value = {}
            mock_vault_instance.get_priority_list.return_value = []
            mock_vault_instance.get_target_utilization.return_value = {}
            mock_vault_instance.get_min_utilization_delta_bips.return_value = 50000
            
            mock_vault.return_value = mock_vault_instance
            
            mock_bot_instance = MagicMock()
            mock_bot_instance.run.return_value = {"status": "success"}
            mock_bot.return_value = mock_bot_instance
            
            # Test Lambda handler
            event = {"source": "aws.events"}
            context = MagicMock()
            
            result = lambda_handler(event, context)
            
            # Verify response
            self.assertEqual(result["statusCode"], 200)
            self.assertIn("vault_name", result["body"])
            
            logger.info("✅ OpenEden Lambda handler test passed")
            
        except Exception as e:
            logger.error(f"❌ OpenEden Lambda handler test failed: {e}")
            raise


def run_all_tests():
    """Run all modular architecture tests."""
    logger.info("🧪 Running modular architecture tests...")
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestVaultFactory,
        TestBaseVault,
        TestConfigurationStructure,
        TestModularBot,
        TestLambdaHandlers
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Report results
    if result.wasSuccessful():
        logger.info("🎉 All modular architecture tests passed!")
        return True
    else:
        logger.error(f"❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
