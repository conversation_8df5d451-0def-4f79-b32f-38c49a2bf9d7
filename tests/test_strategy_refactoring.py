"""
Tests for the refactored strategy system.

This module tests the new vault-agnostic strategy implementation including:
- Dynamic vault configuration loading
- Backward compatibility with legacy configuration
- Strategy initialization with vault names
- Configuration validation
"""

import unittest
import sys
import os
from unittest.mock import patch, MagicMock

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from log import get_logger

logger = get_logger(__name__)


class TestStrategyRefactoring(unittest.TestCase):
    """Test the refactored strategy system."""
    
    def test_strategy_with_openeden_vault(self):
        """Test strategy initialization with OpenEden vault configuration."""
        try:
            from strategies.priority_util.priority_util import PrioritizedUtilizationStrategy
            
            # Test with explicit vault name
            strategy = PrioritizedUtilizationStrategy(vault_name="openeden")
            
            # Verify configuration was loaded
            self.assertIsInstance(strategy.market_names, dict)
            self.assertIsInstance(strategy.priority_list, list)
            self.assertIsInstance(strategy.target_util, dict)
            
            # Verify we have some markets configured
            self.assertGreater(len(strategy.market_names), 0)
            self.assertGreater(len(strategy.priority_list), 0)
            self.assertGreater(len(strategy.target_util), 0)
            
            # Verify that all markets in priority list have target utilization
            for market_id in strategy.priority_list:
                self.assertIn(market_id, strategy.target_util)
            
            logger.info("✅ Strategy with OpenEden vault configuration test passed")
            
        except Exception as e:
            logger.error(f"❌ Strategy with OpenEden vault test failed: {e}")
            raise
    
    def test_strategy_default_vault(self):
        """Test strategy initialization with default vault (backward compatibility)."""
        try:
            from strategies.priority_util.priority_util import PrioritizedUtilizationStrategy
            
            # Test without vault name (should default to openeden)
            strategy = PrioritizedUtilizationStrategy()
            
            # Verify configuration was loaded
            self.assertIsInstance(strategy.market_names, dict)
            self.assertIsInstance(strategy.priority_list, list)
            self.assertIsInstance(strategy.target_util, dict)
            
            # Verify vault name was set to default
            self.assertEqual(strategy.vault_name, "openeden")
            
            logger.info("✅ Strategy default vault test passed")
            
        except Exception as e:
            logger.error(f"❌ Strategy default vault test failed: {e}")
            raise
    
    def test_strategy_invalid_vault(self):
        """Test strategy initialization with invalid vault name."""
        try:
            from strategies.priority_util.priority_util import PrioritizedUtilizationStrategy
            
            # Test with invalid vault name - should fall back to legacy config
            strategy = PrioritizedUtilizationStrategy(vault_name="nonexistent_vault")
            
            # Should still have configuration (from legacy fallback)
            self.assertIsInstance(strategy.market_names, dict)
            self.assertIsInstance(strategy.priority_list, list)
            self.assertIsInstance(strategy.target_util, dict)
            
            logger.info("✅ Strategy invalid vault test passed")
            
        except Exception as e:
            logger.error(f"❌ Strategy invalid vault test failed: {e}")
            raise
    
    def test_vault_config_registry(self):
        """Test the vault configuration registry."""
        try:
            from config.shared.global_config import get_vault_config_module, VAULT_CONFIG_REGISTRY
            
            # Test registry contains OpenEden
            self.assertIn("openeden", VAULT_CONFIG_REGISTRY)
            
            # Test getting OpenEden config module path
            config_path = get_vault_config_module("openeden")
            self.assertEqual(config_path, "config.vaults.openeden.config")
            
            # Test case insensitive lookup
            config_path = get_vault_config_module("OPENEDEN")
            self.assertEqual(config_path, "config.vaults.openeden.config")
            
            # Test nonexistent vault
            config_path = get_vault_config_module("nonexistent")
            self.assertIsNone(config_path)
            
            logger.info("✅ Vault config registry test passed")
            
        except Exception as e:
            logger.error(f"❌ Vault config registry test failed: {e}")
            raise
    
    def test_modular_bot_with_vault_name(self):
        """Test modular bot initialization with vault name."""
        try:
            from src.bot import MorphoReallocatorBot
            from decimal import Decimal
            
            # Test bot with explicit vault name
            bot = MorphoReallocatorBot(
                vault_address='0x1234567890123456789012345678901234567890',
                chain_id=8453,
                market_names={'0xabc': 'Test Market'},
                priority_list=['0xabc'],
                target_util={'0xabc': Decimal('0.9') * Decimal('1e18')},
                min_utilization_delta_bips=50000,
                vault_name="openeden"
            )
            
            # Verify vault name was set
            self.assertEqual(bot.vault_name, "openeden")
            
            # Verify strategy was initialized with vault name
            self.assertEqual(bot.strategy.vault_name, "openeden")
            
            logger.info("✅ Modular bot with vault name test passed")
            
        except Exception as e:
            logger.error(f"❌ Modular bot with vault name test failed: {e}")
            raise
    
    def test_vault_name_auto_detection(self):
        """Test automatic vault name detection by address."""
        try:
            from src.vaults.vault_factory import VaultFactory
            
            # Test with OpenEden vault address
            openeden_address = "0x1D3b1Cd0a0f242d598834b3F2d126dC6bd774657"
            vault_name = VaultFactory.get_vault_name_by_address(openeden_address)
            self.assertEqual(vault_name, "openeden")
            
            # Test with unknown address
            unknown_address = "0x1234567890123456789012345678901234567890"
            vault_name = VaultFactory.get_vault_name_by_address(unknown_address)
            self.assertIsNone(vault_name)
            
            logger.info("✅ Vault name auto-detection test passed")
            
        except Exception as e:
            logger.error(f"❌ Vault name auto-detection test failed: {e}")
            raise
    
    @patch('strategies.priority_util.priority_util.importlib.import_module')
    def test_strategy_config_loading_error_handling(self, mock_import):
        """Test strategy error handling when config loading fails."""
        try:
            from strategies.priority_util.priority_util import PrioritizedUtilizationStrategy
            
            # Mock import failure
            mock_import.side_effect = ImportError("Module not found")
            
            # Should fall back to legacy config
            strategy = PrioritizedUtilizationStrategy(vault_name="test_vault")
            
            # Should still have configuration from legacy fallback
            self.assertIsInstance(strategy.market_names, dict)
            self.assertIsInstance(strategy.priority_list, list)
            self.assertIsInstance(strategy.target_util, dict)
            
            logger.info("✅ Strategy config loading error handling test passed")
            
        except Exception as e:
            logger.error(f"❌ Strategy config loading error handling test failed: {e}")
            raise


def run_strategy_refactoring_tests():
    """Run all strategy refactoring tests."""
    logger.info("🧪 Running strategy refactoring tests...")
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test methods
    test_methods = [
        'test_strategy_with_openeden_vault',
        'test_strategy_default_vault',
        'test_strategy_invalid_vault',
        'test_vault_config_registry',
        'test_modular_bot_with_vault_name',
        'test_vault_name_auto_detection',
        'test_strategy_config_loading_error_handling'
    ]
    
    for test_method in test_methods:
        test_suite.addTest(TestStrategyRefactoring(test_method))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Report results
    if result.wasSuccessful():
        logger.info("🎉 All strategy refactoring tests passed!")
        return True
    else:
        logger.error(f"❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        return False


if __name__ == "__main__":
    success = run_strategy_refactoring_tests()
    sys.exit(0 if success else 1)
