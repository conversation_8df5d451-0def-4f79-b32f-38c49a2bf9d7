# read pem file content from secrets manager and store it as file private.pem

import boto3
from botocore.exceptions import ClientError
import os
import json
from log import get_logger

logger = get_logger(__name__)


def get_secret(cloud_env) -> dict:

    secret_name = cloud_env
    region_name = "eu-central-1"

    # Create a Secrets Manager client
    session = boto3.session.Session()
    client = session.client(service_name="secretsmanager", region_name=region_name)

    try:
        get_secret_value_response = client.get_secret_value(SecretId=secret_name)
    except ClientError as e:
        # For a list of exceptions thrown, see
        # https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_GetSecretValue.html
        raise e

    secret = get_secret_value_response["SecretString"]

    return json.loads(secret)


def load_on_env(secret: dict):
    # set the secret into the environment
    for key, value in secret.items():
        os.environ[key] = value


def load_cloud_env():
    """Load environment variables from AWS Secrets Manager if cloud_env is set."""
    cloud_env = os.getenv("cloud_env")
    if cloud_env and not hasattr(load_cloud_env, "_loaded"):
        try:
            logger.info(f"Loading cloud environment variables from {cloud_env}")
            secret = get_secret(cloud_env)
            load_on_env(secret)
            load_cloud_env._loaded = True
            logger.info("Cloud environment variables loaded successfully")
        except Exception as e:
            logger.warning(f"Failed to load cloud environment variables: {e}")
