# Use the official AWS Lambda Python runtime
FROM public.ecr.aws/lambda/python:3.9

# Copy requirements and install dependencies
COPY requirements.txt ${LAMBDA_TASK_ROOT}/
RUN pip install --no-cache-dir -r requirements.txt

# Copy the entire project into the container
COPY . ${LAMBDA_TASK_ROOT}/

# NOTE: This path to lambda handler is overridden at deploy time in (terraform/modules/vault_lambda/main.tf)
CMD ["src.vaults.openeden.lambda_handler.lambda_handler"]
