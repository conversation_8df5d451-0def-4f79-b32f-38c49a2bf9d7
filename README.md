# Morpho Reallocator Bot

## Overview
The Morpho Reallocator Bot is an automated implementation designed to reallocate funds across markets in Morpho vaults based on target utilization ratios and priority strategies. It monitors market utilization and automatically rebalances allocations to maintain optimal capital efficiency.

The system is built with a focus on:
- **Direct Blockchain Data Access**: Real-time market and vault data fetching via Web3 contract calls
- **Priority-Based Strategy**: Intelligent reallocation based on market priority and target utilization
- **Automated Execution**: Integration with Fordefi for secure transaction execution
- **Interface Compliance**: Strict adherence to defined interfaces for consistency
- **Error Handling**: Comprehensive error handling and retry logic throughout the pipeline
- **Modular Architecture**: Dynamic support for multiple vaults with minimal code changes

## Project Structure
```
morpho-reallocator-bot
├── src/
│   ├── bot.py                    # Main bot logic and reallocation execution
│   ├── market_data.py            # MarketData class using Web3 contract calls
│   ├── vault_data.py             # VaultData class for vault operations
│   ├── interfaces/
│   │   ├── market_interface.py   # Interface definitions for market data
│   │   └── vault_interface.py    # Interface definitions for vault data
│   └── vaults/                   # Modular vault implementations
│       ├── base_vault.py         # Base vault interface
│       ├── vault_factory.py      # Factory for vault instances
│       ├── openeden/             # OpenEden vault implementation
│       └── new_vault/            # Example new vault implementation
├── strategies/
│   ├── strategy.py               # Base class for reallocation strategies
│   ├── equalize_util/
│   │   └── equalize_util.py      # Strategy for equalizing market utilization
│   └── priority_util/
│       └── priority_util.py      # Priority-based utilization strategy (active)
├── utils/
│   ├── math.py                   # Morpho-specific mathematical utilities
│   ├── web3_connector.py         # Web3 provider connections
│   ├── web3_contract.py          # Web3 contract interaction utilities
│   ├── create_transaction.py     # Transaction creation utilities
│   └── fordefi_transaction_helpers.py  # Fordefi integration for secure execution
├── config/
│   ├── shared/
│   │   ├── global_config.py      # Global configuration and vault registry
│   │   └── abis/                 # ABI definitions for contracts
│   └── vaults/                   # Vault-specific configurations
│       ├── openeden/             # OpenEden vault configuration
│       └── new_vault/            # Example new vault configuration
├── tests/
│   ├── simple_test.py            # Basic functionality tests
│   ├── test_current_status.py    # Implementation status verification
│   ├── test_comprehensive.py     # Full test suite
│   ├── test_modular_architecture.py # Tests for modular architecture
│   ├── test_strategy.py          # Comprehensive strategy tests
│   └── test_strategy_refactoring.py # Tests for strategy refactoring
├── README.md                     # This documentation
├── requirements.txt              # Python dependencies
├── load_env.sh                   # Environment variable loading script
├── Dockerfile                    # Docker containerization
└── terraform/                    # Modular Terraform infrastructure
    ├── main.tf                   # Main Terraform configuration
    └── modules/
        └── vault_lambda/         # Reusable module for vault Lambda deployments
```

## Key Features
- **Dynamic Vault Support**: Easily add new vaults by creating configuration and implementation files.
- **Vault Configuration Registry**: Centralized registry for managing vault configurations.
- **Dynamic Strategy Loading**: Strategies dynamically load vault-specific configurations.
- **Backward Compatibility**: Legacy configurations are supported for older vaults.

## Adding a New Vault

To add a new vault to the Morpho Reallocator Bot, follow these steps:

1. **Create Vault Configuration**
   - Add a new configuration file in `config/vaults/<new_vault>/config.py`.
   - Define the vault-specific parameters such as `VAULT_NAME`, `VAULT_ADDRESS`, `market_names`, `priority_list`, and `target_util`.

   Example:
   ```python
   # config/vaults/new_vault/config.py
   VAULT_NAME = "NewVault"
   VAULT_ADDRESS = "0x..."
   FORDEFI_VAULT_ID = os.getenv("FORDEFI_VAULT_ID_NEW")
   PRIMARY_CHAIN_ID = 8453

   market_names = {
       "0x...": "Market 1",
       "0x...": "Market 2",
   }

   priority_list = ["0x...", "0x..."]

   target_util = {
       "0x...": Decimal("0.9") * WAD,
       "0x...": Decimal("0.8") * WAD,
   }
   ```

2. **Create Vault Implementation**
   - Implement the vault in `src/vaults/<new_vault>/vault.py`.
   - Inherit from `BaseVault` and implement required methods.

   Example:
   ```python
   from src.vaults.base_vault import BaseVault
   from config.vaults.new_vault.config import *

   class NewVault(BaseVault):
       def __init__(self):
           vault_config = {
               "VAULT_NAME": VAULT_NAME,
               "VAULT_ADDRESS": VAULT_ADDRESS,
               "PRIMARY_CHAIN_ID": PRIMARY_CHAIN_ID,
               "FORDEFI_VAULT_ID": FORDEFI_VAULT_ID,
           }
           super().__init__(vault_config)

       def get_market_names(self):
           return market_names.copy()

       # Implement other required methods
   ```

3. **Create Lambda Handler**
   - Add a Lambda handler in `src/vaults/<new_vault>/lambda_handler.py`.

   Example:
   ```python
   from src.vaults.new_vault.vault import NewVault
   from src.bot import MorphoReallocatorBot

   def lambda_handler(event, context):
       vault = NewVault()
       bot = MorphoReallocatorBot(
           vault_address=vault.get_vault_address(),
           chain_id=vault.get_chain_id(),
           # ... other parameters
       )
       return bot.run()
   ```

4. **Register in Vault Factory**
   - Update `VaultFactory` in `src/vaults/vault_factory.py` to include the new vault.

   Example:
   ```python
   from src.vaults.new_vault.vault import NewVault

   class VaultFactory:
       _vault_registry = {
           "openeden": OpenEdenVault,
           "new_vault": NewVault,  # Add here
       }
   ```

5. **Add to Terraform**
   - Update `terraform/main.tf` to include the new vault's infrastructure configuration.

   Example:
   ```hcl
   locals {
     vaults = {
       openeden = { ... }
       new_vault = {
         function_name       = "morpho-reallocator-new-vault"
         schedule_expression = "rate(2 hours)"
         timeout            = 600
         memory_size        = 2048
         cloud_env_secret   = "morpho-reallocator-new-vault"
       }
     }
   }
   ```

6. **Deploy the Infrastructure**
   - Use the deployment script or manually deploy the updated infrastructure.

   ```bash
   # Quick deployment
   ./scripts/deploy-modular.sh

   # Manual deployment
   cd terraform
   terraform plan
   terraform apply
   ```

## Strategy System
The strategy system has been refactored to:
- Dynamically load vault-specific configurations.
- Eliminate hardcoded vault-specific imports.
- Validate required configuration attributes during initialization.

### Example Usage
```python
# Initialize strategy for OpenEden vault
strategy = PrioritizedUtilizationStrategy(vault_name="openeden")

# Initialize strategy for a new vault
strategy = PrioritizedUtilizationStrategy(vault_name="new_vault")
```
